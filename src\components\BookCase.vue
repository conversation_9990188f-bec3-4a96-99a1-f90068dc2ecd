<template>
  <bookshelf :data="data" ref="bookshelfRef"></bookshelf>
</template>
<script lang="ts" setup>
import Bookshelf from './Bookshelf';
import { defineProps, watch ,defineExpose,ref,computed} from 'vue';
const { data } = defineProps({
  data: {
    type: Array
  }
});
let bookshelfRef = ref()

let exports = computed(()=> bookshelfRef)
defineExpose({
 exports
})
</script>
<style lang="scss" scoped></style>
