import request from '@/utils/request';
import qs from 'qs';
import store from '@/store';
export default class {
  // 超管权限用户
  static getUserAuth() {
    return request({
      url: '/bookcase/user_auth',
      method: 'get',
    });
  }
  // 当前登录用户
  static getUserInfo() {
    return request({
      url: '/bookcase/get_user',
      method: 'get',
    });
  }
  //   获取书架
  static getBookCase(isAdmin: boolean, params: any) {
    return request({
      // url: isAdmin ? '/bookcase/admin' : '/bookcase/common',
      url: '/bookcase/admin',
      method: 'post',
      data: params,
    });
  }

  // 修改书格名称
  static update(params: any) {
    return request({
      url: '/bookshelf/update',
      method: 'post',
      data: params,
    });
  }
  // 新建文件夹
  static fileFolderAdd(params: any) {
    return request({
      url: '/fileFolder/add',
      method: 'post',
      data: params,
    });
  }
  // 删除文件夹
  static fileFolderDelete(params: any) {
    return request({
      url: '/fileFolder/delete',
      method: 'get',
      params,
    });
  }
  // 修改文件文件夹
  static fileFolderUpdate(params: any) {
    return request({
      url: '/fileFolder/update',
      method: 'post',
      data: params,
    });
  }
  // 上传文件
  static fileFolderUpload(params: any, token: any, onUploadProgress: any) {
    return request({
      url: '/fileFolder/upload',
      method: 'post',
      data: params,
      cancelToken: token,
      // 获取上传进度回调
      onUploadProgress,
    });
  }
  // 获取文件
  static find_fileFolders(params: any) {
    return request({
      url: '/fileFolder/find_fileFolders',
      method: 'get',
      params: {
        ...params,
        bookshelfId: store.state.selectFile.uuid
      },
    });
  }
  // 修改文件集合操作
  static updateBatch(params: any) {
    return request({
      url: '/fileFolder/updateBatch',
      method: 'post',
      data: params,
    });
  }
  // 下载文件
  static download(params: any, onDownloadProgress: any, token: any) {
    return request({
      url: `/fileFolder/download/${params.uuid}`,
      method: 'get',
      responseType: 'blob',
      onDownloadProgress,
      cancelToken: token,
    });
  }
  // 多选下载
  static downloadBatch(params: any, onDownloadProgress: any, token: any) {
    return request({
      url: '/fileFolder/downloadBatch',
      method: 'post',
      data: params,
      responseType: 'blob',
      onDownloadProgress,
      cancelToken: token,
    });
  }
  // 获取移动文件夹
  static findFolders(params: any) {
    return request({
      url: '/fileFolder/findFolders',
      method: 'post',
      data: params,
    });
  }
  // 获取预览uuid
  static getPrevie(params: any) {
    return request({
      url: '/fileFolder/findOne',
      method: 'get',
      params,
    });
  }
  // 权限开放书新建
  static addFolderAuth(params: any) {
    return request({
      url: '/fileFolder/add-folder-auth',
      method: 'post',
      data: params,
    });
  }
  // 预览
  static doPost(params: any) {
    return request({
      url: '/file-preview/doPost',
      method: 'post',
      data: params,
    });
  }
  // 服务器地址
  static find_url(params: any) {
    return request({
      url: '/bookcase/find_url',
      method: 'get',
    });
  }
  // 添加书柜
  static addBookCase(params: any) {
    return request({
      url: '/bookcase/add',
      method: 'post',
      data: params,
    });
  }
  // 修改配置
  static updateAll(params: any) {
    return request({
      url: '/fileFolder/updateAll',
      method: 'post',
      data: params,
    });
  }
  // 获取当前用户下所有书柜
  static findBookCase(params: any) {
    return request({
      url: '/bookcase/find',
      method: 'post',
      data: params,
    });
  }

  // 新修改标签
  static userLevel(params: any) {
    return request({
      url: '/fileFolder/userLevel',
      method: 'post',
      data: params,
    });
  }
  // 新建文件夹验证是否重名
  static checkName(params: any) {
    return request({
      url: '/fileFolder/checkName',
      method: 'post',
      data: params,
    });
  }
  // 上传文件容量验证
  static checkSpace(params: any) {
    return request({
      url: '/bookcase/checkSpace',
      method: 'post',
      params,
    });
  }

  // 查询书架有无关联账号
  static accountByCaseId(params: any) {
    return request({
      url: '/caseAccount/accountByCaseId',
      method: 'get',
      params,
    });
  }
  // 查询二级单位账号
  static findAccountsByOrgs() {
    return request({
      url: '/caseAccount/findAccountsByOrgs',
      method: 'get',
    });
  }

  // 查询云服务是否是管理员
  static findUserLabel(params: any) {
    return request({
      url: '/caseAccount/findUserLabel',
      method: 'get',
    });
  }
  // 跳转服务云注册页面
  static getToken(params: any) {
    return request({
      url: '/tokenController/getToken',
      method: 'get',
    });
  }
  // 判断是否有扩容或新建的代办单
  static applyStatus(params: any) {
    return request({
      url: '/caseAccount/applyStatus',
      method: 'get',
      params,
    });
  }

  // 点击扩容接口
  static applyExpansion(params: any) {
    return request({
      url: '/caseAccount/applyExpansion',
      method: 'post',
      data: params,
    });
  }

  // 点击新建接口
  static applyNewly(params: any) {
    return request({
      url: '/caseAccount/applyNewly',
      method: 'post',
      data: params,
    });
  }

  // 推送接口
  static clickPush(params: any) {
    return request({
      url: '/caseAccount/clickPush',
      method: 'get',
      params,
    });
  }

  // 查询书柜剩余天数
  static queryBookcaseTime(params: any, cancelToken: any) {
    return request({
      url: '/caseAccount/queryBookcaseTime',
      method: 'get',
      params,
      cancelToken,
    });
  }

  static findAccountAdminByOrgs(params: any) {
    return request({
      url: '/caseAccount/findAccountAdminByOrgs',
      method: 'get',
      params,
    });
  }
  // 获取下载文件大小 
  static downloadBatchSpace(params: any) {
    return request({
      url: '/fileFolder/downloadBatchSpace',
      method: 'post',
      data: params
    })
  }

  // 删除提醒开关 
  static notice(params: any) {
    return request({
      url: '/bookcase/notice',
      method: 'post',
      data: params
    })
  }
  // 获取当前书柜删除提醒状态 
  static bookcaseFindOne(params: any) {
    return request({
      url: '/bookcase/findOne',
      method: 'get',
      params
    })
  }

  // 获取书柜移交人员 
  static getUserAndDeptForTrans(params: any) {
    return request({
      url: '/QSecurity/getUserAndDeptForTrans',
      method: 'get',
      params
    })
  }
  // 添加书格
  static bookshelfAdd(params: any) {
    return request({
      url: '/bookshelf/add',
      method: 'post',
      data: params
    })
  }

  // 删除书格
  static bookshelfDelete(params: any) {
    return request({
      url: '/bookshelf/delete',
      method: 'get',
     params
    })
  }
}
