import request from '@/utils/request';
import qs from 'qs';
export default class {
  // 获取书架权限
  static findUserDept(params: any) {
    return request({
      url: '/bookcase/find_user_dept',
      method: 'get',
      params,
    });
  }
  // 增加书架权限
  /**
   *
   * @param params
   * @param type 0用户 1部门
   * @returns
   */
  static insertUser_Dept(params: any, type: number) {
    return request({
      url: type ? '/bookcase/insert_dept' : '/bookcase/insert_user',
      method: 'post',
      data: qs.stringify(params),
    });
  }
  // 删除书架权限
  static deleteUser_Dept(params: any, type: number) {
    return request({
      url: type ? '/bookcase/delete_dept' : '/bookcase//delete_user',
      method: 'get',
      params,
    });
  }
  // 保存书格主人
  static insertShelfAdmin(params: any) {
    return request({
      url: '/bookshelf/insert_shelf_admin',
      method: 'post',
      data: qs.stringify(params),
    });
  }
  // 查询书格主人
  static findShelfAdmin(params: any) {
    return request({
      url: '/bookshelf/find_shelf_admin',
      method: 'get',
      params,
    });
  }

  // 增加书格权限
  /**
   *
   * @param params
   * @param type 0用户 1部门
   * @returns
   */
  static insertShelfUser_Dept(params: any, type: number) {
    return request({
      url: type ? '/bookshelf/insert_dept' : '/bookshelf/insert_user',
      method: 'post',
      data: qs.stringify(params),
    });
  }
  // 删除书格权限
  static deleteShelfUser_Dept(params: any, type: number) {
    return request({
      url: type ? '/bookshelf/delete_dept' : '/bookshelf/delete_user',
      method: 'get',
      params,
    });
  }
  // 获取书格权限
  static shelfUserDept(params: any) {
    return request({
      url: '/bookshelf/find_user_dept',
      method: 'get',
      params,
    });
  }
  // 增加书权限
  /**
   *
   * @param params
   * @param type 0用户 1部门
   * @returns
   */
  static insertFileUser_Dept(params: any, type: number) {
    return request({
      url: type ? '/fileFolder/insert_dept' : '/fileFolder/insert_user',
      method: 'post',
      data: qs.stringify(params),
    });
  }
  // 删除书权限
  static deleteFileUser_Dept(params: any, type: number) {
    return request({
      url: type ? '/fileFolder/delete_dept' : '/fileFolder/delete_user',
      method: 'get',
      params,
    });
  }
  // 获取书权限
  static shelfFileDept(params: any) {
    return request({
      url: '/fileFolder/find_user_dept',
      method: 'get',
      params,
    });
  }
  // 插入文件权限 
  static authBatch(params:any){
    return request({
      url:'/fileFolder/authBatch',
      method:'post',
      data:params
    })
  }
}
