import { ref } from 'vue';
import dayjs from 'dayjs';
export default () => {
  // 转换多久前
  const getDateDiff = (dateTimeStamp: any) => {
    let time = dayjs(dateTimeStamp).valueOf();
    let minute = 1000 * 60; //把分，时，天，周，半个月，一个月用毫秒表示
    let hour = minute * 60;
    let day = hour * 24;
    let week = day * 7;
    let halfamonth = day * 15;
    let month = day * 30;
    let year = month * 12;
    let now = new Date().getTime(); //获取当前时间毫秒
    let diffValue = now - time; //时间差
    if (diffValue < 0) {
      return;
    }
    let minC = diffValue / minute; //计算时间差的分，时，天，周，月
    let hourC = diffValue / hour;
    let dayC = diffValue / day;
    let weekC = diffValue / week;
    let monthC = diffValue / month;
    let yearC = diffValue / month / 12;
    let result = '';
    if (yearC >= 1) {
      result = '' + parseInt(String(yearC)) + '年前';
    } else if (monthC >= 1) {
      result = ' ' + parseInt(String(monthC)) + '月前';
    } else if (weekC >= 1) {
      result = ' ' + parseInt(String(weekC)) + '周前';
    } else if (dayC >= 1) {
      if (dayC == 1) {
        result = '昨天';
      } else {
        result = ' ' + parseInt(String(dayC)) + '天前';
      }
    } else {
      result = '今天';
    }
    return result;
  };
  const acceptList = ref(['doc', 'docx', 'ppt', 'pptx', 'txt', 'xls', 'xlsx', 'pdf', 'gif', 'jpg', 'jpeg', 'png', 'mp4', 'avi', 'wmv', 'mpeg', 'rmvb', 'rm', 'rar', 'zip', '7z']);
  const Arabia_To_SimplifiedChinese = (Num: any) => {
    for (let i = Num.length - 1; i >= 0; i--) {
      Num = Num.replace(',', ''); //替换Num中的“,”
      Num = Num.replace(' ', ''); //替换Num中的空格
    }
    if (isNaN(Num)) {
      //验证输入的字符是否为数字
      //alert("请检查小写金额是否正确");
      return;
    }
    //字符处理完毕后开始转换，采用前后两部分分别转换
    let part = String(Num).split('.');
    let newchar = '';
    //小数点前进行转化
    for (let i = part[0].length - 1; i >= 0; i--) {
      if (part[0].length > 10) {
        //alert("位数过大，无法计算");
        return '';
      } //若数量超过拾亿单位，提示
      let tmpnewchar = '';
      let perchar: any = part[0].charAt(i);
      switch (perchar) {
        case '0':
          tmpnewchar = '零' + tmpnewchar;
          break;
        case '1':
          tmpnewchar = '一' + tmpnewchar;
          break;
        case '2':
          tmpnewchar = '二' + tmpnewchar;
          break;
        case '3':
          tmpnewchar = '三' + tmpnewchar;
          break;
        case '4':
          tmpnewchar = '四' + tmpnewchar;
          break;
        case '5':
          tmpnewchar = '五' + tmpnewchar;
          break;
        case '6':
          tmpnewchar = '六' + tmpnewchar;
          break;
        case '7':
          tmpnewchar = '七' + tmpnewchar;
          break;
        case '8':
          tmpnewchar = '八' + tmpnewchar;
          break;
        case '9':
          tmpnewchar = '九' + tmpnewchar;
          break;
      }
      switch (part[0].length - i - 1) {
        case 0:
          tmpnewchar = tmpnewchar;
          break;
        case 1:
          if (perchar != 0) tmpnewchar = tmpnewchar + '十';
          break;
        case 2:
          if (perchar != 0) tmpnewchar = tmpnewchar + '百';
          break;
        case 3:
          if (perchar != 0) tmpnewchar = tmpnewchar + '千';
          break;
        case 4:
          tmpnewchar = tmpnewchar + '万';
          break;
        case 5:
          if (perchar != 0) tmpnewchar = tmpnewchar + '十';
          break;
        case 6:
          if (perchar != 0) tmpnewchar = tmpnewchar + '百';
          break;
        case 7:
          if (perchar != 0) tmpnewchar = tmpnewchar + '千';
          break;
        case 8:
          tmpnewchar = tmpnewchar + '亿';
          break;
        case 9:
          tmpnewchar = tmpnewchar + '十';
          break;
      }
      newchar = tmpnewchar + newchar;
    }
    //替换所有无用汉字，直到没有此类无用的数字为止
    while (newchar.search('零零') != -1 || newchar.search('零亿') != -1 || newchar.search('亿万') != -1 || newchar.search('零万') != -1) {
      newchar = newchar.replace('零亿', '亿');
      newchar = newchar.replace('亿万', '亿');
      newchar = newchar.replace('零万', '万');
      newchar = newchar.replace('零零', '零');
    }
    //替换以“一十”开头的，为“十”
    if (newchar.indexOf('一十') == 0) {
      newchar = newchar.substr(1);
    }
    //替换以“零”结尾的，为“”
    if (newchar.lastIndexOf('零') == newchar.length - 1) {
      newchar = newchar.substr(0, newchar.length - 1);
    }
    return newchar;
  };
  const getIcon = (val: any) => {
    val = val.toLowerCase();
    const docTypeList = ['doc', 'docx'];
    const pptTypeList = ['ppt', 'pptx'];
    const xlsTypeList = ['xls', 'xlsx'];
    const imgTypeList = ['jpg', 'jpeg', 'png', 'gif'];
    const videoTypeList = ['mp4', 'avi', 'wmv', 'mpeg', 'rmvb', 'rm'];
    const zipTypeList = ['rar', 'zip', '7z'];
    if (docTypeList.includes(val)) {
      return require('@/assets/img/icon/word_table_icon.png');
    } else if (pptTypeList.includes(val)) {
      return require('@/assets/img/icon/ppt_table_icon.png');
    } else if (xlsTypeList.includes(val)) {
      return require('@/assets/img/icon/excel_table_icon.png');
    } else if (imgTypeList.includes(val)) {
      return require('@/assets/img/icon/img_table_icon.png');
    } else if (videoTypeList.includes(val)) {
      return require('@/assets/img/icon/video_table_icon.png');
    } else if (zipTypeList.includes(val)) {
      return require('@/assets/img/icon/zip_table_icon.png');
    } else if (val == 'pdf') {
      return require('@/assets/img/icon/pdf_table_icon.png');
    } else {
      return require('@/assets/img/icon/other_table_icon.png');
    }
  };
  // 格式化单位
  //格式化文件大小
  const renderSize = (value: any, isFloat: 1) => {
    if (null == value || value == '') {
      return '0';
    }
    let unitArr = new Array('Bit', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y');
    let index = 0;
    let srcsize = parseFloat(value);
    index = Math.floor(Math.log(srcsize) / Math.log(1024));
    let size: any = srcsize / Math.pow(1024, index);
    size = isFloat ? size.toFixed(2) : Math.round(size); //保留的小数位数
    if (isFloat) {
      return size + unitArr[index];
    } else {
      if (index) {
        return size + unitArr[index];
      } else {
        return '1K';
      }
    }
  };
  return {
    getDateDiff,
    acceptList,
    Arabia_To_SimplifiedChinese,
    getIcon,
    renderSize,
  };
};
