<template>
  <a-modal width="300px" :footer="null" v-model:visible="params.visible" title="新建文件夹" @cancel="close" :maskClosable="false">
    <div class="content newLabel">
      <a-input style="height:30px" id="refShelfName" size="small" v-model:value="newFileName" @keypress.enter="handleOk" />
      <div class="save" @click="handleOk">保存</div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import { defineProps, ref, defineEmits, computed,onMounted } from "vue";
import { useStore } from "vuex";
import utils from "@/utils/utils";
import indexApi from "@/api/Index";
import { ElMessage as message } from "element-plus";
const { params, fileList } = defineProps({
  params: {
    type: Object,
  },
  fileList: {
    type: Array,
  },
});
const store = useStore();
onMounted(()=>{
  $("#refShelfName").focus();
})
const emit = defineEmits(["close"]);
let newFileName = ref("");
const close = (val) => {
  emit("close", { params, isAdd: val });
};
const handleOk = async () => {
  let saveparams = {
    fileName: newFileName.value,
    uuid: utils.uuid(),
    type: "folder",
    level: params.level + 1,
    bookcaseId: store.state.bookCase.uuid,
    parentId: params.uuid,
  };
  if (!newFileName.value) return message.warning("名称不能为空!");
  const { data, code, msg } = await indexApi.fileFolderAdd(saveparams);
  if (code == 200) {
    close(1);
  } else {
    message.warning(msg);
  }
};
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.newLabel {
  display: flex;
  align-items: center;
  height: 40px !important;
  padding: 10px !important;
  .save {
    height: 30px;
    line-height: 30px;
    flex: 0 0 50px;
    text-align: center;
    background-color: #3162a7;
    color: #fff;
    border-radius: 4px;
    margin-left: 4px;
    cursor: pointer;
  }
  .title {
    font-size: $fontSize;
  }
  .el-input {
    margin-top: 0 !important;
  }
  .footer {
    display: flex;
    margin-top: 40px;
    justify-content: center;
    .ant-btn {
      color: #fff;
      margin-right: 10px;
    }
    .ant-btn-primary {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}
</style>