$fontSize: 12px;
.ant-modal-header {
  padding: 10px 16px !important;
  background-color: #3161a9;
  border-bottom: none;
  .ant-modal-title {
    color: #ffffff !important;
    font-weight: 700;
    font-size: $fontSize;
  }
}
.ant-modal-close-x {
  width: 43px !important;
  height: 43px !important;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff !important;
}

.ant-modal-body {
  border: 1px solid #3161a9;
  border-top: none;
  padding: 0 !important;
  background-color: #e9f0f9;
}
.saveFlod {
  .ant-modal-confirm-content {
    padding: 20px !important;
    padding-top: 30px !important;
    padding-bottom: 30px !important;
  }
}

.alert .anticon,
.alert .ant-modal-close {
  display: none !important;
}

.alerts {
  top: 260px !important;
}

.selectAccount {
  top: 20%;
}

.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {
  background-color: rgba(0, 0, 0, 0.45) !important;
}
.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {
  background-color: rgba(0, 0, 0, 0.45) !important;
}

.ant-rate-star-first,
.ant-rate-star-second {
  color: #ddd;
}

.myErrorMsg {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url('../img/icon/book_del.png') no-repeat center center;
  background-size: 100% 100%;
  vertical-align: middle;
  border-radius: 4px;
}

.ant-message-error .anticon-close-circle {
  display: none;
}

.my_ant_msg.ant-message {
  top: auto !important;
  bottom: 0 !important;
}

.my_ant_msg {
  .ant-message-notice-content {
    border: 1px solid #ff1400;
    border-radius: 4px;
  }
}

.com_users {
  .ant-modal-confirm-content {
    white-space: pre-line;
  }
  .ant-modal-confirm-btns {
    display: none;
  }
}

.confirm {
  top: 200px !important;
  .ant-btn {
    &:nth-child(1) {
      display: none;
    }
  }
}

.noAdminAlert {
  .ant-modal-confirm-btns {
    justify-content: center !important;
  }
  .ant-modal-confirm-content {
    padding-bottom: 0 !important;
  }
}

.dti_title {
  font-size: 20px !important;
}

.dti_content {
  font-size: 14px !important;
  margin-bottom: 14px;
  text-align: right;
}

.dti_phone {
  text-align: left;
  margin-left: 209px;
}
.ant-progress-bg {
  height: 4px !important;
}

.contactInformation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 409px;
  background-color: #d6e1f1;
  border-radius: 4px;
  margin-bottom: 20px;
  div {
    line-height: 40px;
    height: 40px;
  }
}

.dti_pro_title {
  font-size: 12px !important;
  text-align: center !important;
}
.dti_pro_title.dti_v_title {
  font-size: 14px !important;
}

.dialog_alert{
  background: url('../img/icon/alert.png') no-repeat center center;
  background-size: 100% 100%;
}
.dti_alert {
  font-size: 12px !important;
}

.ant-switch-checked{
  background-color:#3161a9;
}