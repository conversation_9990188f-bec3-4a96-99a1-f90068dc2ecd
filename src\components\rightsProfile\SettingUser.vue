<template>
  <a-modal :maskClosable="false" width="650px" :footer="null" v-model:visible="params.visible" :title="params.title" @cancel="closeDialog">
    <div class="content">
      <ul>
        <li>
          <div class="dialog_empty" v-show="!users">
            <div class="empty_content">
              <img src="../../assets/img/empty.svg" />
              <span>{{step[type]}}</span>
            </div>
          </div>
          <div class="ztree" id="tree" v-show="users"></div>
        </li>
        <li>
          <div class="select_list">
            <div class="aside_title">{{params.alert}}</div>
            <div class="item" v-for="(item,index) in selectList" :key="item.id">
              <div class="li_left">
                <i class="type_icon" v-if="item.type!='dept'"></i>
                <i class="dept_icon" v-else></i>
                <span :title="item.name">{{item.name}}</span>
              </div>
              <i class="delete" @click="deleteUser_Dept(item)"></i>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="footer">
      <div class="btn" @click="close">确定</div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import {
  ref,
  defineEmits,
  defineProps,
  watch,
  nextTick,
  onMounted,
  inject,
} from "vue";
import settingApi from "@/api/setting";
import { ElMessage as message } from "element-plus";

import { useStore } from "vuex";
const store = useStore();
const emit = defineEmits(["close"]);
const { params, getList, userDeptList, type } = defineProps({
  /**
   * 1书架权限配置
   * 2书格权限配置
   *
   */
  type: {
    type: Number,
  },
  params: {
    type: Object,
  },
  getList: {
    type: Function,
  },
  userDeptList: {
    type: Array,
  },
});
let users = ref(false);
let zTree: any = null;
let step = {
  2: "请在书柜权限配置按钮中配置人员或部门",
  3: "请在书格权限配置按钮中配置人员或部门",
};
// 右侧选中
const selectList: any = ref([]);
// 新增用户部门
const insertUser_Dept = async (val: any) => {
  const interfaceObj: any = {
    1: "insertUser_Dept",
    2: "insertShelfUser_Dept",
    3: "insertFileUser_Dept",
  };
  if (type == 4 || type == 5) {
    selectList.value.push({
      name: val.name,
      id: val.id,
      type: val.type,
    });
  } else {
    let paramsObj = {
      [val.type == "dept" ? "deptName" : "userName"]: val.name,
    };
    switch (type) {
      case 1:
        paramsObj.bookcaseId = store.state.bookCase.uuid;
        paramsObj[val.type == "dept" ? "bookcaseDeptId" : "bookcaseUserId"] =
          val.id;
        break;
      case 2:
        paramsObj.shelfId = params.uuid;
        paramsObj[val.type == "dept" ? "deptId" : "userId"] = val.id;
        break;
      case 3:
        paramsObj.fileUuid = params.fileUuid;
        paramsObj[val.type == "dept" ? "deptId" : "userId"] = val.id;
        break;
      default:
        break;
    }

    const { data, code, msg } = await settingApi[interfaceObj[type]](
      paramsObj,
      val.type == "dept" ? 1 : 0
    );
    if (code == 200) {
      let userDept = {
        name: val.name,
        id: val.id,
        uuid: data,
        type: val.type,
      };
      if (val.type == "dept") {
        let index = selectList.value.findIndex((item) => {
          return item.type !== "dept";
        });
        selectList.value.splice(index, 0, userDept);
      } else {
        selectList.value.push(userDept);
      }
    } else {
      message.warning(msg);
    }
  }
};
// 取消页面选中
const deletePageSelect = (val) => {
  let node = zTree.getNodeByParam("id", val.id);
  if (node != null) {
    zTree.checkNode(node, false); //将指定ID的节点不选中
  }
  let index;
  if (type == 4 || type == 5) {
    index = selectList.value.findIndex((item) => {
      return item.id == val.id;
    });
  } else {
    index = selectList.value.findIndex((item) => {
      return item.uuid == val.uuid;
    });
  }

  selectList.value.splice(index, 1);
};
// 删除用户部门
const deleteUser_Dept = async (val: any) => {
  if (type == 4 || type == 5) {
    deletePageSelect(val);
  } else {
    const interfaceObj: any = {
      1: "deleteUser_Dept",
      2: "deleteShelfUser_Dept",
      3: "deleteFileUser_Dept",
    };
    let params = {
      uuid: val.uuid,
    };
    const { data, code, msg } = await settingApi[interfaceObj[type]](
      params,
      val.type == "dept" ? 1 : 0
    );
    if (code == 200) {
      deletePageSelect(val);
    } else {
      message.warning(msg);
    }
  }
};
let ZTreeSetting = {
  async: {
    enable: true,
    autoParam: ["id"],
    type: "get",
    url:
      process.env.NODE_ENV === "development"
        ? "/QSecurity/getUserAndDept"
        : "/jchc-edisk/QSecurity/getUserAndDept",
    dataFilter: function (treeId, parentNode, res) {
      !users.value && res.data.length ? (users.value = true) : "";
      var childNodes = res.data;
      res.data.forEach((item: any) => {
        if (
          selectList.value &&
          selectList.value.findIndex((items) => {
            return items.id == item.id;
          }) !== -1
        ) {
          item.checked = true;
        }
      });
      return childNodes;
    },
  },
  check: {
    enable: true,
    chkboxType: { Y: "", N: "" },
  },
  data: {
    key: {
      title: "name",
    },
    simpleData: {
      enable: true,
      idKey: "id",
      pIdKey: "Pid",
      rootPId: "-1",
    },
  },
  callback: {
    onCheck: function (event, zTreeId, node) {
      if (node.checked) {
        insertUser_Dept(node);
      } else {
        console.log(selectList.value);
        let el = selectList.value.find((item) => {
          return item.id == node.id;
        });
        console.log(el);
        deleteUser_Dept(el);
      }
    },
  },
};
const initZtree = () => {
  console.log(ZTreeSetting);
  zTree = $.fn.zTree.init($("#tree"), ZTreeSetting);
};
// 获取书格权限人员
const getShelfUserDept = async (fn) => {
  const interfaceObj: any = {
    2: "shelfUserDept",
    3: "shelfFileDept",
  };
  let paramsObj = {};
  switch (type) {
    case 2:
      paramsObj.shelfId = params.uuid;
      break;
    case 3:
      paramsObj.fileFolderId = params.fileUuid;
      break;
    default:
      break;
  }

  const { data, code, msg } = await settingApi[interfaceObj[type]](paramsObj);
  if (code == 200) {
    selectList.value = data;
    fn();
  } else {
    message.warning(msg);
  }
};

onMounted(() => {
  switch (type) {
    case 1:
      selectList.value = userDeptList
        ? JSON.parse(JSON.stringify(userDeptList))
        : "";
      initZtree();
      break;
    case 2:
      getShelfUserDept(() => {
        ZTreeSetting.async.url =
          process.env.NODE_ENV === "development"
            ? "/QSecurity/getUserAndDeptForShelfOnlyRead"
            : "/jchc-edisk/QSecurity/getUserAndDeptForShelfOnlyRead";
        ZTreeSetting.async.otherParam = [
          "bookcaseId",
          store.state.bookCase.uuid,
          "bookshelfId",
          store.state.selectFile.uuid,
        ];
        initZtree();
      });
      break;
    case 3:
      getShelfUserDept(() => {
        ZTreeSetting.async.url =
          process.env.NODE_ENV === "development"
            ? "/QSecurity/getUserAndDeptForFolder"
            : "/jchc-edisk/QSecurity/getUserAndDeptForFolder";
        ZTreeSetting.async.otherParam = ["bookshelfId", params.uuid];
        initZtree();
      });
      break;
    case 4:
      ZTreeSetting.async.url =
        process.env.NODE_ENV === "development"
          ? "/QSecurity/getUserAndDeptForFile"
          : "/jchc-edisk/QSecurity/getUserAndDeptForFile";
      ZTreeSetting.async.otherParam = ["folderId", params.uuid];
      initZtree();
    case 5:
      params.filterCheck.forEach((item) => {
        item.linkedUser.forEach((items) => {
          let index = selectList.value.findIndex((e) => {
            return e.id == items.id;
          });
          if (index == -1) {
            if (items.type == "dept") {
              let index = selectList.value.findIndex((e) => {
                return e.type !== "dept";
              });
              selectList.value.splice(index, 0, items);
            } else {
              selectList.value.push(items);
            }
          }
        });
      });
      ZTreeSetting.async.url =
        process.env.NODE_ENV === "development"
          ? "/QSecurity/getUserAndDeptForFile"
          : "/jchc-edisk/QSecurity/getUserAndDeptForFile";
      ZTreeSetting.async.otherParam = ["folderId", params.uuid];
      initZtree();
    default:
      break;
  }
});
const editDataList = inject("editDataList");
const getUserAuth = inject("getUserAuth");
const editFile = () => {
  let paramsObj = {
    uuids: params.filterCheck.map((item) => {
      return item.uuid;
    }),
    userDepts: selectList.value,
  };
  settingApi.authBatch(paramsObj).then((res) => {
    if (res.code == 200) {
      params.filterCheck.forEach((item) => {
        item.linkedUser = selectList.value;
        item.check = false;
        editDataList(item, 1);
      });
    } else {
      message.warning(res.msg);
    }
  });
};
const close = () => {
  switch (type) {
    case 1:
      getList();
      break;
    case 2:
      getUserAuth();
      break;
    case 4:
      editFile();
      break;
    case 5:
      editFile();
      break;
    default:
      break;
  }
  closeDialog();
};
const closeDialog = () => {
  zTree ? zTree.destroy() : "";
  if (type == 2) {
    store.commit("setSelectFolder", {});
  }
  emit("close", false);
};
let checked = ref(false);
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.aside_title {
  font-size: $fontSize;
  color: #3162a7;
  height: 30px;
  line-height: 40px;
  padding: 0 9px;
}
.content {
  padding: 0 23px;
  padding-top: 20px;
  font-size: $fontSize;
  .title {
    margin-bottom: 17px;
    font-size: $fontSize;
    color: #333;
  }
  ul {
    display: flex;
    margin-top: 10px;
    margin-bottom: 25px;
    color: #333;
    li {
      flex: 1;
      width: 0;
      .zTree_title {
        margin-bottom: 19px;
        span {
          color: #3162a7;
        }
      }

      &:nth-child(1) {
        margin-right: 20px;
      }
      .ztree,
      .select_list {
        background-color: #fff;
        height: 396px;
        overflow-y: auto;

        .item {
          display: flex;
          // margin-top: 15px;
          // height: 27px;
          align-items: center;
          justify-content: space-between;
          padding: 0 18px 0 14px;
          .delete {
            display: inline-block;
            width: 14px;
            height: 16px;
            background: url("../../assets/img/icon/delete_icon.png") no-repeat
              center center;
            background-size: 100% 100%;
            cursor: pointer;
            flex: 0 0 14px;
          }
        }
      }
    }
  }
}
.type_icon {
  display: inline-block;
  width: 13px;
  height: 15px;
  background: url("../../assets/img/icon/user_dept.png") no-repeat center center;
  background-size: 100% 100%;
  margin-right: 5px;
}
.dept_icon {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: url("../../assets/img/icon/dept_icon.png") no-repeat center center;
  background-size: 100% 100%;
  margin-right: 5px;
  flex: 0 0 11px;
}
.li_left {
  display: flex;
  align-items: center;
  width: 90%;
  span {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.dialog_empty {
  position: relative;
  background-color: #fff;
  width: 100%;
  height: 100%;
  .empty_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    img {
      display: inline-block;
      width: 50px;
    }
    span {
      margin-top: 10px;
      display: block;
      width: auto;
      white-space: nowrap;
      color: #dbdbdb;
    }
  }
}
</style>
<style lang="scss" scoped>
.footer {
  overflow: hidden;
  padding-bottom: 22px;
  padding-right: 22px;
  .btn {
    float: right;
    background-color: #3162a7;
    color: #fff !important;
    width: 60px;
  }
}
</style>