<template>
  <div class="uploadLoadingContent">
    <div class="uploadLoading">
      <i :class="!downLoadType ? 'fileNow' :'fileOk'"></i>
      <div class="title">
        {{downParams?.percentage!=100 ? '正在下载' :'上传完成'}}
        <span v-if="downParams?.percentage!=100 " style="margin-left:17px">预计剩余时间：<span style="color:#3161A9">{{downParams?.time}}</span></span>
      </div>
      <CloseOutlined style="color:#999" @click="handleRemove(item,index)" />
    </div>
    <a-progress :percent="downParams?.percentage" />
  </div>

</template>
<script lang="ts" setup>
import { ref, defineProps, defineEmits } from "vue";
import { Modal } from "ant-design-vue";
import { CloseOutlined, UpOutlined } from "@ant-design/icons-vue";
const { downParams } = defineProps({
  downParams: {
    type: Object,
  },
});
const emit = defineEmits(["removeDown"]);
const handleRemove = () => {
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "终止下载",
    cancelText: () => "取消",
    title: () => "取消下载",
    content: () => "确定要放弃下载吗？",
    onOk() {
      emit("removeDown");
    },
  });
};
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.uploadLoadingContent {
  position: relative;
  width: 392px;
  padding: 0 16px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 5px 14px 0px rgba(60, 60, 60, 0.3);
  margin-bottom: 20px;
}
.uploadLoading {
  height: 40px;
  margin-bottom: 10px;
  padding-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  i {
    display: inline-block;
    width: 23px;
    height: 23px;
    background: url("../assets/img/icon/fileOk_icon.png") no-repeat center
      center;
    background-size: 100% 100%;
  }
  .fileNow {
    background-image: url("../assets/img/icon/fileNow_icon.png");
    transform: rotate(180deg);
  }

  .anticon-close {
    margin-right: 14px;
  }
  .title {
    margin-left: 20px;
    flex: 1;
    font-size: $fontSize;
    span {
      color: #999;
    }
  }
}
</style>