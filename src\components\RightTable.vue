<template>
  <div id="RightTable" class="RightTable" v-if="selectFolder?.uuid" :class="!tableData.length ? 'gray':''">
    <div class="buttons">
      <div class="left_btns">
        <span class="btn_bg" :class="(!isCreate||store.state.file.showUpload) ?'disabled':''" @click="uploadFile">上传文件</span>
        <span class="btn" :class="!isCreate || !isHasCreate || !isUser   ?'disabled':''" @click="addNewFolder(1)" v-if="isAdd">新建文件夹</span>
      </div>
      <div class="right_btns">
        <pre>{{store.state.file.downDisabled}}</pre>
        <span class="btn" @click="move" :class="(!tableData.length || isCheck ) ? 'disabled':''">移动</span>
        <span class="btn red_style" @click="deleteTableList" :class="(!tableData.length || isCheck) ? 'disabled':''">删除</span>
        <span class="btn" :class="(!tableData.length || isCheck || store.state.file.showDownLoad)   ? 'disabled':''" @click="downLoad">下载</span>
        <span class="btn" :class="(!tableData.length || isLevel) ? 'disabled':''" @click="settingUser">查看权限</span>
      </div>
    </div>
    <div class="table">
      <disk-table :editLevel="editLevel" :editDataList="editDataList" @sort="sort" :searchParams="searchParams" :tableData="tableData" :maxHeight="maxHeight" @remove="remove" ref="diskRef"></disk-table>
    </div>
  </div>
  <div class="empty" v-else>
    <div>
      <img src="../assets/img/background/fload.png" alt="">
      <span>您还没打开任何文件夹</span>
    </div>
  </div>
  <Upload :acceptList="acceptList" @sendFileList="fileUpload" ref="uploadRef"></Upload>
  <KeyWords :fileList="fileList" :params="keyWordsParams" @close="closeKeyWords" v-if="keyWordsParams.visible"></KeyWords>
  <moveLog v-if="moveParams.visible" :params="moveParams" @close="closeMoveLog"></moveLog>
  <newLabel @close="editLabelClose" v-if="lebelParams.visible" :params="lebelParams" :type="1"></newLabel>
  <SettingUser :type="4" v-if="settingUserParams.visible" :params="settingUserParams" @close="closeSettingUser"></SettingUser>
  <DownLoad v-if="showDown" :downParams="downParams" @removeDown="removeDown"></DownLoad>
</template>
<script setup lang="ts">
import { CloseOutlined, UpOutlined } from "@ant-design/icons-vue";
import {
  nextTick,
  ref,
  reactive,
  computed,
  onMounted,
  onUnmounted,
  watch,
  inject,
  provide,
  defineExpose
} from "vue";
import { Modal } from "ant-design-vue";
import { ElMessage as message } from "element-plus";
import DiskTable from "./table/DiskTable";
import Upload from "./upload/Upload.vue";
import $ from "jquery";
import hooks from "@/hooks/commonHooks";
import KeyWords from "./alert/keyWords.vue";
import moveLog from "./alert/move";
import { useStore } from "vuex";
import utils from "@/utils/utils";
import indexApi from "@/api/Index";
import axios from "axios";
import SettingUser from "./rightsProfile/SettingUser.vue";
import newLabel from "./alert/newLabel.vue";
import dayjs from "dayjs";
import DownLoad from "@/components/DownLoad.vue";
const leftParams = inject("searchParams");
const getUserAuth = inject("getUserAuth");
const store = useStore();
const isAdmin = computed(() => store.state.selectFolder.isAdmin);
const userInfo = computed(() => store.state.userInfo);
const selectFile = computed(() => store.state.selectFile);
// 左侧书本选中
const selectFolder = computed(() => store.state.selectFolder);
// 右侧复选框文件夹选中
const rightSelectFolder = computed(() => store.state.rightSelectFolder);
// 异步加载文件夹
const asyncFolder = computed(() => store.state.asyncFolder);

// 是否是普通成员
const isUser = computed(() => {
  let isHandle = true;
  if (!isAdmin.value && userInfo.value.id != selectFile.value.userId) {
    isHandle = false;
  }
  return isHandle;
});

const { acceptList, getIcon } = hooks();
// 上传组件
const uploadRef = ref(null);
// 表格组件
const diskRef = ref(null);

let maxHeight = ref(0);

// 获取表格容器的高度
const getTableHeight = () => {
  maxHeight.value = $(".RightTable").height() - 150;
};
// 是否可以操作移动删除下载权限配置权限 true 不能下载
let isCheck = ref(true);
// 是否可以上传文件和新建文件夹权限 true可以上传
let isCreate = ref(true);
// 是否可以选择标签  true 不能选择
let isLevel = ref(true);
let isHasCreate = ref(true);

// 禁用下载
let downDisabled = ref(false);

// 是否显示新建按钮
let isAdd = ref(true);

// 标签状态重置
const resetIs = () => {
  isLevel.value = true;
  isCheck.value = true;
  isCreate.value = true;
  isAdd.value = true;
};
const editLevel = (val) => {
  // 选中有权限的文件夹
  let selectFolder = [];
  // 选中的文件
  let selectFile = [];
  // 选中没权限的文件夹
  let selectFolderCanEdit = [];

  diskRef.value.table.forEach((item) => {
    if (item.check && item.type == "folder" && item.canEdit) {
      selectFolder.push(item);
    } else if (item.check && item.type != "folder" && item.canEdit) {
      selectFile.push(item);
    } else if (item.check && item.type == "folder" && !item.canEdit) {
      selectFolderCanEdit.push(item);
    }
  });
  if (
    !selectFolder.length &&
    !selectFile.length &&
    !selectFolderCanEdit.length
  ) {
    resetIs();
  } else if (
    selectFolder.length &&
    selectFile.length &&
    selectFolderCanEdit.length
  ) {
    isAdd.value = false;
    isCreate.value = false;
    isCheck.value = true;
    isLevel.value = true;
  } else if (
    selectFolder.length &&
    selectFile.length &&
    !selectFolderCanEdit.length
  ) {
    isAdd.value = false;
    isCreate.value = false;
    isCheck.value = false;
    isLevel.value = true;
  } else if (
    selectFolder.length &&
    !selectFile.length &&
    !selectFolderCanEdit.length
  ) {
    if (selectFolder.length == 1) {
      isAdd.value = false;
      isCreate.value = true;
      isCheck.value = false;
      isLevel.value = true;
      store.commit("setRightSelectFolder", selectFolder[0]);
    } else {
      isAdd.value = false;
      isCreate.value = false;
      isCheck.value = false;
      isLevel.value = true;
    }
  } else if (
    !selectFolder.length &&
    !selectFile.length &&
    selectFolderCanEdit.length
  ) {
    if (selectFolderCanEdit.length == 1) {
      resetIs();
      store.commit("setRightSelectFolder", selectFolderCanEdit[0]);
    } else {
      isAdd.value = false;

      isCreate.value = false;
      isCheck.value = true;
      isLevel.value = true;
    }
  } else if (
    !selectFolder.length &&
    selectFile.length &&
    !selectFolderCanEdit.length
  ) {
    isAdd.value = false;
    isCreate.value = false;
    isCheck.value = false;
    isLevel.value = false;
  } else if (
    !selectFolder.length &&
    selectFile.length &&
    selectFolderCanEdit.length
  ) {
    isAdd.value = false;
    isCreate.value = false;
    isCheck.value = true;
    isLevel.value = true;
  }
};

let level = ref(0);
let tableData: any = ref([]);

// 右侧表格查询参数
let searchParams = reactive({
  fileNameSort: "ASC",
  totalSpaceSort: "",
  dataCreateTimeSort: "",
  folderId: "",
  bookcaseId: "",
  shelfUserId: "",
});

const find_fileFolders = async (type, fn) => {
  // 获取当前用户是否为普通用户
  if (
    !store.state.isAdmin &&
    store.state.selectFile.userId !== store.state.userInfo.id
  ) {
    isHasCreate.value = store.state.selectFile.linkedUser.some((item) => {
      return (
        item.id == store.state.userInfo.id ||
        item.id == store.state.userInfo.deptId
      );
    });
  }

  /**
   *1 展开显示
   */
  searchParams.folderId = asyncFolder.value.uuid
    ? asyncFolder.value.uuid
    : selectFolder.value.uuid;
  searchParams.shelfUserId = store.state.selectFile.userId
    ? store.state.selectFile.userId
    : "";
  searchParams.bookcaseId = store.state.bookCase.uuid;
  searchParams.level === 0 ? (searchParams.level = "") : "";

  let params = JSON.parse(JSON.stringify(searchParams));
  params = {
    ...params,
    ...leftParams,
  };
  const { data, code, msg } = await indexApi.find_fileFolders(params);
  if (code == 200) {
    switch (type) {
      case 1:
        // data.forEach((item) => {
        //   item.show = true;
        // });
        fn(data);
        break;
      default:
        tableData.value = data;
        diskRef.value.initTable(tableData.value);
        break;
    }
    diskRef.value.checkAll = false;
  } else {
    message.warning(msg);
  }
};
// 修改数据
/**
 * 1 修改
 * 2 异步新增
 * 3 上传文件
 */
const editDataList = (val, type, fileList) => {
  const deep = (table, val) => {
    table.forEach((item) => {
      if (item.uuid == val.uuid) {
        switch (type) {
          case 1:
            item.uuid = val.uuid;
            item.fileName = val.fileName;
            item.type = val.type;
            item.bookcaseId = val.bookcaseId;
            item.parentId = val.parentId;
            item.level = val.level;
            item.isAdd = false;
            item.linkedUser = val.linkedUser;
            item.totalSpace = val.totalSpace;
            item.keyword = val.keyword;
            item.check = val.check;
            item.canEdit = val.canEdit;
            diskRef.value.initTable(tableData.value);
            diskRef.value.checkAll = false;
            break;
          case 2:
            find_fileFolders(1, (dataList) => {
              item.children = dataList;
              diskRef.value.initTable(tableData.value);
            });
            break;
          case 3:
            let select = diskRef.value.table.find((items) => {
              return items.uuid == item.uuid;
            });
            if (select.unfold) {
              // 当前文件夹如果展开直接合并children
              let index = item.children
                .map((e) => e.type)
                .lastIndexOf("folder");
              fileList.forEach((items) => {
                items.show = true;
                item.children.splice(index == -1 ? 0 : index + 1, 0, items);
              });
              // item.children = [...fileList, ...item.children];
              diskRef.value.initTable(tableData.value);
            } else {
              // 没有展开从接口直接取children 并且展开该节点
              find_fileFolders(1, (dataList) => {
                item.unfold = true;
                dataList.forEach((item) => {
                  item.show = true;
                });
                item.children = dataList;
                diskRef.value.initTable(tableData.value);
              });
            }
            break;
          default:
            break;
        }
      } else if (item.children && item.children.length) {
        deep(item.children, val);
      }
    });
  };
  deep(tableData.value, val);
};

provide("editDataList", editDataList);
// 配置人员参数
let settingUserParams = reactive({
  visible: false,
  title: "文件权限配置",
  alert: "拥有文件查看权限的人员或部门",
  // 书本id
  uuid: "",
  // 选中文件
  filterCheck: [],
});
const settingUser = () => {
  if (!tableData.value.length || isLevel.value) return;
  settingUserParams.filterCheck = diskRef.value.table.filter((item) => {
    return item.check;
  });
  settingUserParams.uuid = selectFolder.value.uuid;
  settingUserParams.visible = true;
};
const closeSettingUser = () => {
  settingUserParams.visible = false;
};
// 修改等级
const updateLevel = async () => {
  let filterCheck = diskRef.value.table.filter((item) => {
    return item.check;
  });
  const { data, code, msg } = await indexApi.updateBatch({
    uuids: filterCheck.map((item) => {
      return item.uuid;
    }),
    type: "level",
    level: level.value,
  });
  if (code == 200) {
    filterCheck.forEach((item) => {
      item.level = level.value;
      item.check = false;
      editDataList(item, 1);
    });
    level.value = 0;
  } else {
    message.warning(msg);
  }
};

// 表格排序
const sort = (val) => {
  store.commit("setClear");
  switch (val.type) {
    case "fileNameSort":
      searchParams.fileNameSort =
        searchParams.fileNameSort == val.sort ? "" : val.sort;
      searchParams.totalSpaceSort = "";
      searchParams.dataCreateTimeSort = "";
      break;
    case "totalSpaceSort":
      searchParams.totalSpaceSort =
        searchParams.totalSpaceSort == val.sort ? "" : val.sort;
      searchParams.fileNameSort = "";
      searchParams.dataCreateTimeSort = "";
      break;
    case "dataCreateTimeSort":
      searchParams.dataCreateTimeSort =
        searchParams.dataCreateTimeSort == val.sort ? "" : val.sort;
      searchParams.fileNameSort = "";
      searchParams.totalSpaceSort = "";
      break;
    default:
      break;
  }
  find_fileFolders();
};
const initRightTable = () => {
  tableData.value = [];
  find_fileFolders();
  nextTick(() => {
    getTableHeight();
    window.addEventListener("resize", () => {
      getTableHeight();
    });
  });
};
watch(selectFolder, (newVal) => {
  if (newVal.uuid) {
    resetIs();
    initRightTable();
  }
});
const clearAdd = () => {
  if (!diskRef.value) return;
  let newAdd = diskRef.value.table.filter((item) => {
    return item.isAdd;
  });
  const deepRemove = (val, list) => {
    list.forEach((item, index) => {
      if (item.uuid == val.uuid) {
        list.splice(index, 1);
        diskRef.value.initTable(tableData.value);
      } else if (item.children && item.children.length) {
        deepRemove(val, item.children);
      }
    });
  };
  if (!newAdd.length) return;
  deepRemove(newAdd[0], tableData.value);
};
provide("initRightTable", initRightTable);
provide("clearAdd", clearAdd);
// 点击其他地方清除新建文件夹框
onMounted(() => {
  document.addEventListener("click", (e) => {
    if (
      !$(e.target).closest(
        ".submit,.ant-input,.fileName,.btn,.delete_icon,.add_flod"
      ).length
    ) {
      clearAdd();
    }
  });
});
const getTime = (time: any) => {
  let secondTime = parseInt(time); // 秒
  let minuteTime = 0; // 分
  if (secondTime >= 60) {
    minuteTime = parseInt(secondTime / 60);
    secondTime = parseInt(secondTime % 60);
    if (minuteTime >= 60) {
      hourTime = parseInt(minuteTime / 60);
      minuteTime = parseInt(minuteTime % 60);
    }
  }
  var result =
    "" +
    (parseInt(secondTime) < 10
      ? "0" + parseInt(secondTime)
      : parseInt(secondTime));

  result =
    "" +
    (parseInt(minuteTime) < 10
      ? "0" + parseInt(minuteTime)
      : parseInt(minuteTime)) +
    ":" +
    result;

  return result;
};

onUnmounted(() => {
  window.removeEventListener("resize", () => {});
  document.removeEventListener("click", () => {});
});
// 打开新建文件夹弹框
/**
 * 1 最外层
 * 2 掉接口查一遍
 */
const addNewFolder = (val, uuid) => {
  if (!isCreate.value || !isHasCreate.value || !isUser.value) return;
  let params = {
    fileName: "",
    isAdd: true,
    uuid: utils.uuid(),
    type: "folder",
    level: 0,
    bookcaseId: store.state.bookCase.uuid,
    parentId: "",
    canEdit: "",
  };
  if (val == 1) {
    // 在最外层创建文件夹
    if (diskRef.value.table.findIndex((item) => item.isAdd) == -1) {
      params.parentId = selectFolder.value.uuid;
      tableData.value.unshift(params);
      diskRef.value.initTable(tableData.value);
    }
  } else {
    // 在内部创建
    params.parentId = uuid;
    params.canEdit = true;
    params.show = true;
    const deep = (table, id) => {
      table.forEach((item) => {
        if (item.uuid == id) {
          // 找到节点插入
          let select = diskRef.value.table.find((item) => {
            return item.uuid == uuid;
          });
          store.commit("setAsyncFolder", item);
          if (select.unfold) {
            item.children = [params, ...item.children];
            diskRef.value.initTable(tableData.value);
            store.commit("setClear");
          } else {
            find_fileFolders(1, (dataList) => {
              dataList.forEach((items) => {
                items.show = true;
                items.unfold = false;
              });
              item.children = [params, ...dataList];
              item.unfold = true;
              item.check = false;
              diskRef.value.initTable(tableData.value);
              store.commit("setClear");
            });
          }
        } else if (item.children && item.children.length) {
          deep(item.children, id);
        }
      });
    };
    deep(tableData.value, params.parentId);
  }
};

provide("addNewFolder", addNewFolder);
const tableDataValue = () => {
  const deep = (table) => {
    table.forEach((item) => {
      delete item.show;
      delete item.unfold;
      if (item.children && item.children.length) {
        deep(item.children);
      }
    });
  };
  deep(tableData.value);
};
provide("tableDataValue", tableDataValue);

const remove = (val) => {
  const deepRemove = (val, list) => {
    list.forEach((item, index) => {
      if (item.uuid == val.uuid) {
        list.splice(index, 1);
        diskRef.value.initTable(tableData.value);
        store.commit("setClear");
      } else if (item.children && item.children.length) {
        deepRemove(val, item.children);
      }
    });
  };
  deepRemove(val, tableData.value);
};
const deleteTableList = () => {
  if (!tableData.value.length || isCheck.value) return;
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "确定删除",
    content: () => "确定删除该文件吗?删除后在30天内可以通过回收站还原",
    onOk() {
      let deleteList = diskRef.value.table.filter((item) => {
        return item.check;
      });
      indexApi
        .updateBatch({
          uuids: deleteList.map((item) => {
            return item.uuid;
          }),
          type: "delete",
        })
        .then((res) => {
          if (res.code == 200) {
            resetIs();
            deleteList.forEach((item) => {
              remove(item);
            });
            getUserAuth();
          } else {
            message.warning(res.msg);
          }
        });
    },
    onCancel() {},
  });
};
// 移动弹框
const moveParams = reactive({
  // 选中文件
  filterCheck: [],
  visible: false,
});
const move = (val: any) => {
  if (!tableData.value.length || isCheck.value) return;
  moveParams.filterCheck = diskRef.value.table.filter((item) => {
    return item.check;
  });
  moveParams.visible = true;
};

// 关键字弹框
const keyWordsParams = reactive({
  visible: false,
});

// let showUpload = ref(false);
const uploadFile = () => {
  if (!isCreate.value || store.state.file.showUpload) return;
  uploadRef.value.selectFile();
};

// // 上传文件列表是否展开
// let showFileList = ref(false);
// // 上传文件个数
// let fileLength = ref(0);
// // 已上传文件list
// let fileList = ref([]);
// // 上传请求存储令牌
// let tokenList: any = ref([]);
// // 请求令牌
// const CancelToken = axios.CancelToken;
// // 展开上传列表
// const handleShowFileList = () => {
//   showFileList.value = !showFileList.value;
// };
// // 剩余时间
// let surplusTime = ref();
// // 上传总百分比
// let allPercentage = ref(0);
// // 上传文件预估时间
// const progressTime = {
//   allTotal: 0,
//   //上一次计算时间
//   lastTime: 0,
//   //上一次计算的文件大小
//   lastSize: 0,
//   init() {
//     this.allTotal = 0;
//     this.lastTime = 0;
//     this.lastSize = 0;
//   },
//   progressHandle(event) {
//     let nowTime = new Date().getTime();
//     let intervalTime = (nowTime - this.lastTime) / 1000;
//     let lastSize = this.lastSize;
//     this.lastSize += event.size;
//     let intervalSize = this.lastSize - lastSize;
//     this.lastTime = nowTime;
//     let speed = intervalSize / intervalTime;
//     let bSpeed = 512000;
//     surplusTime.value =
//       (this.allTotal - this.lastSize) / bSpeed > 0
//         ? getTime((this.allTotal - this.lastSize) / bSpeed)
//         : getTime(1);
//     allPercentage.value =
//       (this.lastSize / this.allTotal) * 100 >= 100
//         ? 99
//         : (this.lastSize / this.allTotal) * 100;
//   },
// };

// const temFileList = ref<any>([]);
const fileUploadShow: any = inject("fileUpload");
// // 上传附件
const fileUpload = (val: {
  files: [];
  uploadSpace: number;
  type: number;
  bookcaseId: string;
  parentId: any;
  linkedUser: any;
  rightSelectFolder: any;
  selectFolder: any;
}) => {
  val.type = 1;
  val.linkedUser = store.state.selectFile.linkedUser;
  val.bookcaseId = store.state.selectFile.bookcaseId;
  val.parentId = rightSelectFolder.value.uuid
    ? rightSelectFolder.value.uuid
    : selectFolder.value.uuid;
  val.rightSelectFolder = rightSelectFolder.value;
  val.selectFolder = selectFolder.value;
  store.commit("file/setUpLoad", val);
  fileUploadShow(val, {
    getUserAuth,
    editDataList,
    resetIs,
    resetCheck: () => {
      diskRef.value?.initTable(tableData.value);
    },
    pusTableData: (val) => {
      let index = tableData.value.map((e) => e.type).lastIndexOf("folder");
      val.forEach((item) => {
        tableData.value.splice(index == -1 ? 0 : index + 1, 0, item);
      });
      console.log(tableData.value)
    },
  });
  //   progressTime.allTotal = val.uploadSpace;
  //   showUpload.value = true;
  //   fileLength.value = val.files.length;
  //   temFileList.value = [];
  //   tokenList.value = [];
  //   for (let i = 0; i < val.files.length; i++) {
  //     let key = utils.uuid();
  //     temFileList.value.push({
  //       name: val.files[i].name,
  //       percent: 0,
  //       total: 0,
  //       lastSize: 0,
  //       key,
  //       alltotal: val.files[i].size,
  //       type: val.files[i].name.split(".")[
  //         val.files[i].name.split(".").length - 1
  //       ],
  //     });
  //     let formData = new FormData();
  //     formData.append("filecontent", val.files[i]);
  //     formData.append("level", "0");
  //     formData.append("bookcaseId", store.state.selectFile.bookcaseId);
  //     formData.append(
  //       "parentId",
  //       rightSelectFolder.value.uuid
  //         ? rightSelectFolder.value.uuid
  //         : selectFolder.value.uuid
  //     );
  //     let token = new CancelToken(function executor(c) {
  //       tokenList.value.push({ id: key, c });
  //     });
  //     // 上传附件回调
  //     const onUploadProgress = (val) => {
  //       let index = temFileList.value.findIndex((item) => item.key == key);
  //       if (index != -1) {
  //         temFileList.value[index].lastSize =
  //           val.loaded - temFileList.value[index].lastSize;
  //         val.size = temFileList.value[index].lastSize;
  //         temFileList.value[index].lastSize = val.loaded;
  //         progressTime.progressHandle(val);
  //         temFileList.value[index].total = val.loaded;
  //         temFileList.value[index].percent =
  //           (val.loaded / val.total) * 100 != 100
  //             ? (val.loaded / val.total) * 100
  //             : 99 | 0;
  //       }
};

//     indexApi
//       .fileFolderUpload(formData, token, onUploadProgress)
//       .then((res: any) => {
//         if (res.code == 200) {
//           fileLength.value--;
//           res.data.canEdit = true;
//           res.data.linkedUser = store.state.selectFile.linkedUser;
//           let index = temFileList.value.findIndex((item) => item.key == key);
//           if (index !== -1) {
//             temFileList.value[index].percent = 100;
//           }
//           fileList.value.push(res.data);
//           if (!fileLength.value) {
//             closeFile();
//             allPercentage.value = 100;
//           }
//         }
//       });
//   }
// };
// // 单文件取消上传
// const handleRemove = (val, index) => {
//   Modal.confirm({
//     closable: true,
//     keyboard: false,
//     width: 413,
//     class: "alert",
//     okText: () => "终止上传",
//     cancelText: () => "取消",
//     title: () => "取消上传",
//     content: () => "确定要放弃上传吗？",
//     onOk() {
//       let findToken = tokenList.value.find((item) => item.id == val.key);
//       findToken.c({ msg: "取消文件上传" });
//       progressTime.allTotal =
//         progressTime.allTotal - temFileList.value[index].alltotal;
//       progressTime.lastSize =
//         progressTime.lastSize - temFileList.value[index].lastSize;
//       progressTime.progressHandle({ size: 0 });
//       temFileList.value.splice(index, 1);
//       fileLength.value -= 1;
//     },
//     onCancel() {},
//   });
// };

// const resetFileParams = () => {
//   temFileList.value = [];
//   surplusTime.value = 0;
//   showFileList.value = false;
//   progressTime.init();
//   showUpload.value = false;
//   fileLength.value = 0;
//   fileList.value = [];
// };

// watch(fileLength, (newVal) => {
//   if (!newVal) {
//     if (
//       fileList.value.length &&
//       fileList.value.length == temFileList.value.length
//     ) {
//       openKeyWords();
//     } else {
//       resetFileParams();
//     }
//   }
// });
// // 上传文件参数重置
// const uploadRest = () => {
//   tokenList.value.forEach((item: any) => {
//     item.c({ msg: "取消文件上传" });
//   });
//   resetFileParams();
//   getUserAuth();
// };

// const openKeyWords = () => {
//   keyWordsParams.visible = true;
// };
// const closeKeyWords = (val: any) => {
//   if (val.keyword) {
//     fileList.value.forEach((item) => {
//       item.keyword = val.keyword;
//     });
//   }
//   if (rightSelectFolder.value.uuid) {
//     store.commit("setAsyncFolder", rightSelectFolder.value);
//     editDataList(rightSelectFolder.value, 3);
//   } else {
//     fileList.value.forEach((item) => {
//       tableData.value.push(item);
//     });
//     diskRef.value.initTable(tableData.value);
//   }
//   store.commit("setClear");
//   uploadRest();
//   keyWordsParams.visible = false;
// };

const closeMoveLog = (val: any) => {
  moveParams.visible = false;
  if (val) {
    diskRef.value.resetOld();
    store.commit("setClear");
    resetIs();
    initRightTable();
    getUserAuth();
  }
};
// // 关闭上传框
// const closeFile = () => {
//   if (fileLength.value) {
//     Modal.confirm({
//       closable: true,
//       keyboard: false,
//       width: 413,
//       class: "alert",
//       okText: () => "终止上传",
//       cancelText: () => "取消",
//       title: () => "取消上传",
//       content: () => "列表中有未完成上传的文件，确定要放弃上传吗？",
//       onOk() {
//         uploadRest();
//       },
//       onCancel() {},
//     });
//   } else {
//     openKeyWords();
//   }
// };

// let showDown = ref(false);

// const downParams = reactive({
//   time: getTime(0),
//   percentage: 0,
// });
// let lastTime = 0;
// let lastSize = 0;
// let downTimes = null;
// let downtotal = 0;
// const downProgressHandle = (event) => {
//   let nowTime = new Date().getTime();
//   let intervalTime = (nowTime - lastTime) / 1000;
//   let intervalSize = event.loaded - lastSize;
//   lastTime = nowTime;
//   lastSize = event.loaded;
//   let speed = intervalSize / intervalTime;
//   let bSpeed = 512000;
//   if (downTimes) {
//     return;
//   }
//   downTimes = setTimeout(() => {
//     downParams.time = getTime(
//       (downtotal - event.loaded) / bSpeed > 0
//         ? (downtotal - event.loaded) / bSpeed
//         : 0
//     );
//     downTimes = null;
//   }, 1500);
//   console.log("下载量", event.loaded, "总量", downtotal);
//   downParams.percentage = (event.loaded / downtotal) * 100;
// };

// let downtoken = null;
// const resetDownParams = () => {
//   downDisabled.value = false;
//   showDown.value = false;
//   downParams.time = 0;
//   downParams.percentage = 0;
//   lastTime = 0;
//   lastSize = 0;
//   downtotal = 0;
//   downTimes = null;
// };
const showDownLoad = inject("downLoad");
// 多选下载
const downLoad = async () => {
  if (!tableData.value.length || isCheck.value || store.state.file.showDownLoad)
    return;
  store.commit("file/setDownLoad", {
    type: 1,
    filterCheck: diskRef.value.table.filter((item) => item.check),
  });
  showDownLoad({
    resetIs: resetIs,
    resetCheck: () => {
      diskRef.value?.initTable(tableData.value);
    },
  });
};

const lebelParams = reactive({
  visible: false,
  filterCheck: [],
});
const editLevels = () => {
  if (!isLevel.value && tableData.value.length) {
    lebelParams.visible = true;
    lebelParams.filterCheck = diskRef.value.table.filter((item) => {
      return item.check && !item.folder;
    });
  }
};
const editLabelClose = () => {
  editLevel();
  lebelParams.visible = false;
};
defineExpose({resetIs,initRightTable})
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.RightTable {
  width: 40%;
  height: 100%;
  overflow-y: auto;
  padding: 0 10px;
  .buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 25px;
    .left_btns {
      display: flex;
      > span {
        width: 82px;
      }
    }
    .right_btns {
      display: flex;
      > span {
        width: 60px;
        &:nth-child(5) {
          width: 82px;
        }
      }
    }
  }
  .breadcrumb {
    margin-top: 18px;
  }
  .table {
    margin-top: 20px;
  }
  .el-table {
    margin-top: 18px;
    border: 1px solid #ebeef5;
    border-bottom: none;
  }
  .el-pagination {
    margin-top: 44px;
    text-align: right;
  }
}
</style>
