<template>
  <div class="dialog_content contents content_slot">
    <iframe :src="src" frameborder="0" allowtransparency="true"></iframe>
  </div>
</template>
<script lang="ts">
export default {
  name: "PreviewArea",
  props: {
    src: {
      type: String,
      default: "https://sjkwd.jchc.cn/view/rXYhrZf",
    },
  },
};
</script>
<style lang="scss" scoped>
.dialog_content {
  height: 100%;
  width: 100%;
  iframe {
    width: 100%;
    height: 100%;
  }
}
</style>