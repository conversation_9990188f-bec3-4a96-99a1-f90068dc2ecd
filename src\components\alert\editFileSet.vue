<template>
  <a-modal width="650px" :footer="null" v-model:visible="params.visible" title="文件配置" @cancel="close" :maskClosable="false">
    <div class="newLabel">
      <el-form label-position="top" label-width="100px" :model="formParams">
        <el-row>
          <el-col :span="12">
            <el-form-item label="重命名" style="margin-right:10px">
              <el-input placeholder="请输入文件名" style="width:100%" size="small" v-model="formParams.fileName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关键词" style="margin-left:10px">
              <el-input maxlength="50" placeholder="请输入关键词" style="width:100%" size="small" v-model="formParams.keyword"></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item class="label_item" label="标签" style="margin-bottom:10px">
              <div class="tips" title="通过选择星星，对该文件进行标签设置">
                <ExclamationOutlined />
              </div>
              <a-rate v-model:value="formParams.level" :count="3"></a-rate>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <ul class="file_user">
        <li>
          <div class="dialog_empty" v-show="!users">
            <div class="empty_content">
              <img src="../../assets/img/empty.svg" />
              <span>请在书格权限配置按钮中配置人员或部门</span>
            </div>
          </div>
          <div class="ztree" id="tree" v-show="users"></div>
        </li>
        <li>
          <div class="select_list">
            <div class="aside_title">拥有该文件查看权限的人员或部门</div>
            <div class="item" v-for="(item,index) in formParams.linkedUser" :key="item.id">
              <div class="li_left">
                <i class="type_icon" v-if="item.type!='dept'"></i>
                <i class="dept_icon" v-else></i>
                <span :title="item.name">{{item.name}}</span>
              </div>
              <i class="delete" @click="deleteUser_Dept(item)"></i>
            </div>
          </div>
        </li>
      </ul>
      <div class="footer alert">
        <div class="ant-btn-primary" @click="handleOk">确定 </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import {
  defineProps,
  ref,
  defineEmits,
  computed,
  inject,
  onMounted,
  onBeforeUnmount,
  reactive,
} from "vue";
import indexApi from "@/api/Index";
import { ElMessage as message } from "element-plus";
import { Modal, Empty } from "ant-design-vue";
import { ExclamationOutlined } from "@ant-design/icons-vue";
import { useStore } from "vuex";
const store = useStore();
const { params } = defineProps({
  params: {
    type: Object,
  },
});
const editDataList = inject("editDataList");
const emit = defineEmits(["close"]);
let keyword = ref("");
let formParams = reactive(JSON.parse(JSON.stringify(params)));
const close = (val) => {
  emit("close");
};
const insertUser_Dept = async (val: any) => {
  let userDept = {
    name: val.name,
    id: val.id,
    type: val.type,
  };
  if (val.type == "dept") {
    let index = formParams.linkedUser.findIndex((item) => {
      return item.type !== "dept";
    });
    formParams.linkedUser.splice(index, 0, userDept);
  } else {
    formParams.linkedUser.push(userDept);
  }
};

let users = ref(false);
let zTree = null;
let ZTreeSetting = {
  async: {
    enable: true,
    autoParam: ["id"],
    type: "get",
    url:
      process.env.NODE_ENV === "development"
        ? "/QSecurity/getUserAndDeptForFolder"
        : "/jchc-edisk/QSecurity/getUserAndDeptForFolder",
    dataFilter: function (treeId, parentNode, res) {
      !users.value && res.data.length ? (users.value = true) : "";
      var childNodes = res.data;
      res.data.forEach((item: any) => {
        if (
          formParams.linkedUser &&
          formParams.linkedUser.findIndex((items) => {
            return items.id == item.id;
          }) !== -1
        ) {
          item.checked = true;
        }
      });
      return childNodes;
    },
  },
  check: {
    enable: true,
    chkboxType: { Y: "", N: "" },
  },
  data: {
    key: {
      title: "name",
    },
    simpleData: {
      enable: true,
      idKey: "id",
      pIdKey: "Pid",
      rootPId: "-1",
    },
  },
  callback: {
    onCheck: function (event, zTreeId, node) {
      if (node.checked) {
        insertUser_Dept(node);
      } else {
        let el = formParams.linkedUser.find((item) => {
          return item.id == node.id;
        });
        deleteUser_Dept(el);
      }
    },
  },
};
const deleteUser_Dept = (val) => {
  let node = zTree.getNodeByParam("id", val.id);
  if (node != null) {
    zTree.checkNode(node, false); //将指定ID的节点不选中
  }
  let index = formParams.linkedUser.findIndex((item) => {
    return item.id == val.id;
  });

  formParams.linkedUser.splice(index, 1);
};
const initZtree = () => {
  ZTreeSetting.async.otherParam = ["bookshelfId", store.state.selectFile.uuid];
  zTree = $.fn.zTree.init($("#tree"), ZTreeSetting);
};
onMounted(() => {
  $(document).click(function (e: any) {
    const arr = [
      "newLabel",
      "rates",
      "el-form-item__content",
      "el-form-item__label",
      "el-row",
    ];
    if (arr.includes($(e.target).attr("class"))) {
      formParams.level = 0;
    }
  });
  initZtree();
});
onBeforeUnmount(() => {
  $(document).unbind();
});
const handleOk = async () => {
  if (!formParams.fileName.trim()) return message.warning("请输入文件名称!");
  formParams.linkedUser.forEach((item) => {
    item.linkedId = formParams.uuid;
  });
  const { data, code, msg } = await indexApi.updateAll(formParams);
  if (code == 200) {
    editDataList(formParams, 1);
    close();
  } else {
    message.warning(msg);
  }
};
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.newLabel {
  height: auto !important;
  padding: 27px 19px 20px 19px;
  .title {
    font-size: $fontSize;
  }
  .el-input {
    margin-top: 22px;
  }
  .footer {
    display: flex;
    margin-top: 15px !important;
    justify-content: flex-end !important;
    .ant-btn {
      color: #fff;
      margin-right: 10px;
    }
    .ant-btn-primary {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .el-rate {
    display: inline-block;
    margin-top: 18px;
    // display: flex;
    // justify-content: center;
    // width: 100%;
    // margin-top: 40px;
    .el-rate__icon {
      font-size: 30px !important;
      svg {
        font-size: 1em !important;
      }
    }
  }
}
.tips {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
  border: 2px solid #606266;
  border-radius: 50%;
  top: -21px;
  left: 28px;
  transform: scale(0.7);
  .anticon {
    transform: scale(0.7);
  }
}
.file_user {
  display: flex;
  color: #333;
  li {
    flex: 1;
    width: 0;
    .li_left {
      display: flex;
      align-items: center;
      width: 90%;
      span {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .zTree_title {
      margin-bottom: 19px;
      span {
        color: #3162a7;
      }
    }

    &:nth-child(1) {
      margin-right: 20px;
    }
    .ztree,
    .select_list {
      background-color: #fff;
      height: 390px;
      overflow-y: auto;
      .item {
        display: flex;
        // margin-top: 15px;
        // height: 27px;
        align-items: center;
        justify-content: space-between;
        padding: 0 18px 0 14px;
        .delete {
          display: inline-block;
          width: 14px;
          height: 16px;
          background: url("../../assets/img/icon/delete_icon.png") no-repeat
            center center;
          background-size: 100% 100%;
          cursor: pointer;
          flex: 0 0 14px;
        }
      }
    }
  }
}
.type_icon {
  display: inline-block;
  width: 13px;
  height: 15px;
  background: url("../../assets/img/icon/user_dept.png") no-repeat center center;
  background-size: 100% 100%;
  margin-right: 5px;
}
.dept_icon {
  display: inline-block;
  width: 11px;
  height: 11px;
  background: url("../../assets/img/icon/dept_icon.png") no-repeat center center;
  background-size: 100% 100%;
  margin-right: 5px;
  flex: 0 0 11px;
}
.aside_title {
  font-size: $fontSize;
  color: #3162a7;
  height: 40px;
  line-height: 40px;
  padding: 0 9px;
}
.label_item {
  .el-form-item__content {
    margin-top: 10px;
  }
}
.dialog_empty {
  position: relative;
  background-color: #fff;
  width: 100%;
  height: 100%;
  .empty_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    img {
      display: inline-block;
      width: 50px;
    }
    span {
      margin-top: 10px;
      display: block;
      white-space: nowrap;
      color: #dbdbdb;
    }
  }
}
.ant-rate {
  margin-top: 10px;
}
</style>
<style>
.newLabel .anticon-star svg {
  font-size: 24px !important;
}
.newLabel .ant-rate-star-zero svg {
  color: #c2c7ce;
}
</style>