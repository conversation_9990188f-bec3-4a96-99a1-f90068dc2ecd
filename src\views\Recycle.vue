<template>
  <div class="recycle">
    <div class="left_content">
      <common-header :type="2" @searchBook="searchBook">
        <template #left_title>
          <div class="title">回收站</div>
        </template>
      </common-header>
      <div class="book_content">
        <div class="left_bg" @click="currentBookCase(0)"></div>
      <div class="pre-btn swiper-btn" @click="currentSwiper(0)" v-if="!bookcaseDisabled && showPrev"></div>
        <div class="swiper book_case recycle_book_case swiper-no-swiping">
          <div class="swiper-wrapper">
            <div class="swiper-slide" v-for="(item, index) in bookCaseList" :key="item.uuid">
              <book-case-com :ref="($event: any) => {
                setRef($event, index)
              }" :data="item.bookshelfVos"  :key="item.uuid"></book-case-com>
               <!-- :key="()=>{new Date().valueOf()}" -->
            </div>
          </div>
            <!-- 切换按钮 -->
            <div class="next-btn swiper-btn" @click="currentSwiper(1)" v-if="!bookcaseDisabled && showNext">
            </div>
        </div>
        <div class="right_bg" @click="currentBookCase(1)"></div>
      </div>
    </div>
    <right-table></right-table>
  </div>
</template>
<script lang="ts" setup>
import { message } from "ant-design-vue";
import {
  nextTick,
  reactive,
  ref,
  computed,
  provide,
  onUnmounted,
  watch,
} from "vue";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import indexApi from "@/api/recycle";
import RightTable from "@/components/recycle/RightTable";
import CommonHeader from "@/components/CommonHeader";
import BookCaseCom from "@/components/recycle/BookCase";
import utils from "@/utils/utils";
const route = useRoute();
const store = useStore();
const bookCase = computed(() => store.state.bookCase);
const bookCaseList: any = ref([]);
const bookcaseDisabled = computed(() => store.state.disabled);
const searchParams: any = reactive({
  keyword: "",
  fileName: "",
  bookcaseId: "",
  recycleBin: true,
});
provide("searchParams", searchParams);

let swiper;
let index;
const getBookCase = async (val = false) => {
  const { data, code, msg } = await indexApi.findRecycleBin(searchParams);
  if (code == 200) {
    if (data instanceof Array) {
      data.forEach((item) => {
        if (item.bookshelfVos.length < 5) {
          let num = 5 - item.bookshelfVos.length;
          for (let i = 0; i < num; i++) {
            item.bookshelfVos.push({ isLabel: 1, uuid: utils.uuid() });
          }
        }
      });
      bookCaseList.value = data;
      // 当前书柜如果不在第一个则切换到上传位置
      index = data.findIndex((item) => {
        return item.uuid == store.state.bookCase.uuid;
      });
      store.commit("setBookCase", index !== -1 ? data[index] : data[0]);
    } else {
      bookCaseList.value = [data];
      store.commit("setBookCase", data);
    }
    nextTick(() => {
      if (swiper) {
        swiper.update();
        swiper.slideTo(index);
      } else {
        swiper = new Swiper(".recycle_book_case", {
          direction: "horizontal",
          slidesPerView: "auto",
          centeredSlides: true,
          roundLengths: true,
          loop: false,
          on: {
            slideChangeTransitionStart: function () {
              console.log(this.activeIndex);
              bookCaseIndex.value= this.activeIndex
              store.commit("setBookCase", bookCaseList.value[this.activeIndex]);
            },
          },
        });
        swiper.update();
        swiper.slideTo(index);
      }
    });
  } else {
    message.warning(msg);
  }
};
provide('getBookCase',getBookCase)
const getUser = async () => {
  const { data, code, msg } = await indexApi.getUserInfo();
  if (code == 200) {
    store.commit("setUserInfo", data);
    let isAdmin = store.state.userAuth.some((item) => {
      return item.uid === data.account;
    });
    console.log(isAdmin);
    store.commit("setAdmin", isAdmin);
    getBookCase(isAdmin);
  } else {
    message.warning(msg);
  }
};
const getUserAuth = async () => {
  const { data, code, msg } = await indexApi.getUserAuth();
  if (code == 200) {
    store.commit("setUserAuth", data);
    getUser();
  } else {
    message.warning(msg);
  }
};

getUserAuth();
// 修改书格配置调用刷新接口
provide("getUserAuth", getUserAuth);
const clear = () => {
  store.commit("setClear");
  store.commit("setSelectFile", {});
  store.commit("setSelectFolder", {});
  // swiper ? swiper.slideTo(0) : "";
};
const searchBook = (val: any) => {
  searchParams.keyword = val.keyword;
  clear();
  store.commit("clearBooCase");
  getUserAuth();
};
onUnmounted(() => {
  clear();
  // store.commit("clearBooCase");
});
let bookCaseIndex = ref(0)
// 添加书柜
const currentBookCase = (val: number) => {
  val ? swiper.slideNext() : swiper.slidePrev();
};
const swiperBookCaseRefs: any = ref([])
const setRef = (el: any, index: string | number) => {
  if (el) {
    swiperBookCaseRefs.value[index] = el
  }
}
let retrunSwiper = () => {
  return bookCaseList.value.length==1? swiperBookCaseRefs.value[bookCaseIndex.value].exports.value.swiper :  swiperBookCaseRefs.value[bookCaseIndex.value].exports.value.swiper[bookCaseIndex.value]
}
// 当前显示的书格索引
let activeIndex = ref(0)
// 是否显示向上按钮
let showPrev = computed(() => Number(activeIndex.value))
// 是否显示向下按钮
let showNext = computed(() => {
  if (bookCaseList.value.length) {
    return Number(activeIndex.value) + 5 < bookCaseList.value[bookCaseIndex.value].bookshelfVos.length

  }
})
watch(bookCaseIndex, (newVal, oldVal) => {
  activeIndex.value = 0
  nextTick(() => {
    let swiper = retrunSwiper()
    swiper.update()
    swiper.slideTo(0, 0, false);
  })
},{
  deep:true
})
// 切换书格
const currentSwiper = (val: any) => {
  let swiper = retrunSwiper()
  if (val) {
    // 向下切换
    swiper.slideNext()
    activeIndex.value = swiper.activeIndex
  } else {
    // 向上切换
    swiper.slidePrev()
    activeIndex.value = swiper.activeIndex
  }
}
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.left_bg {
  background: url("../assets/img/background/left_color_bg_recycle.png")
    no-repeat center center;
  background-size: 100% 100%;
  cursor: pointer;
}
.right_bg {
  background: url("../assets/img/background/right_color_bg_recycle.png")
    no-repeat center center;
  background-size: 100% 100%;
  cursor: pointer;
}
.recycle {
  display: flex;
  height: 100%;
  width: 100%;
  .left_content {
    width: 60%;
    height: 100%;
    background-color: #212731;
    .title {
      font-size: 16px !important;
      font-weight: bold;
      color: #fff;
    }
    .book_content {
      position: relative;
      height: calc(100% - 60px);
      width: 100%;
      .book_case {
        height: 100%;
        // .swiper {
        //   width: 100%;
        //   height: 100%;
        // }
        // .swiper-slide {
        //   width: 83% !important;
        //   font-size: $fontSize;
        //   transition: 300ms;
        //   transform: scale(0.95);
        //   filter: blur(1px);
        // }
        // .swiper-slide-active,
        // .swiper-slide-duplicate-active {
        //   transform: scale(1);
        //   filter: none !important;
        // }
      }
      .book_case {
        position: relative;
        width: 83%;
        height: 100%;
      }

      .swiper-slide {
        text-align: center;
        font-size: 18px;
        background: #474b52;

        /* Center slide text vertically */
        display: -webkit-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
      }
    }
  }
}

.pre-btn {
  transform:translateX(-50%) rotate(180deg) !important;
  width: 35px;
  height: 31px;
  background: url('../assets/img/background/curr-recycle.png') no-repeat center center;
  background-size: 100% 100%;

  &:hover {
    background: url('../assets/img/background/curr-recycle-hover.png') no-repeat center center;
    background-size: 100% 100%;
  }
}

.swiper-btn {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  font-size: 24px !important;
  z-index: 22;
  cursor: pointer;
}

.next-btn {
  bottom: 4px;
  width: 35px;
  height: 31px;
  background: url('../assets/img/background/curr-recycle.png') no-repeat center center;
  background-size: 100% 100%;

  &:hover {
    background: url('../assets/img/background/curr-recycle-hover.png') no-repeat center center;
    background-size: 100% 100%;
  }
}
</style>
