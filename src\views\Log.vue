<template>
  <div class="log">
    <div class="log_title">
      <span class="back" @click="back">
        <left-outlined style="color:#B4B4B4;margin-right:9px" />操作日志
      </span>
      <span class="export_btn" @click="downLoad">导出日志</span>
    </div>
    <div class="search_params">
      <div class="search_params_left">
        <a-form layout="inline" :model="searchParams">
          <a-form-item label="时间：">
            <el-date-picker :disabledDate="disabledDate" style="width:220px" format="YYYY-MM-DD" value-format="YYYY-MM-DD" v-model="date" size="small" type="daterange" @change="clearDate">
            </el-date-picker>
          </a-form-item>
          <a-form-item label="操作类型：">
            <el-select v-model="searchParams.operateTypeId" size="small">
              <el-option :label="item.name" :value="item.uuid" v-for="(item,index) in findAllChildrenList" :key="item.uuid"></el-option>
            </el-select>
          </a-form-item>
          <a-form-item label="关键字：">
            <el-input style="width:280px" v-model="searchParams.keyWord" size="small" placeholder="请输入账号、用户名称搜索" />
          </a-form-item>
          <a-form-item>
            <div class="btn" @click="searchReport">查询</div>
          </a-form-item>
        </a-form>
      </div>
      <div class="search_params_right">
        <div class="btn" :class="item.id==searchParams.selectDate ? 'btn' :'btns'" v-for="(item,index) in filterDate" :key="index" @click="currentDate(item)">{{item.name}}</div>
      </div>
    </div>
    <table class="log_table">
      <colgroup>
        <col width="47 " />
        <col width="132px" />
        <col width="132px" />
        <col />
        <col width="201px" />
      </colgroup>
      <thead>
        <tr>
          <th>序号</th>
          <th>用户名称</th>
          <th>操作类型</th>
          <th>操作详情</th>
          <th>操作时间</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item,index) in dataList" :key="item.uuid">
          <td>{{(searchParams.curr - 1) * searchParams.limit +index+1}}</td>
          <td>{{item.userName}}</td>
          <td>{{item.operateType}}</td>
          <td>
            <div class="cell" :title="item.operateReason">{{item.operateReason}}</div>
          </td>
          <td>{{item.dataCreateTime}}</td>
        </tr>
      </tbody>
    </table>
    <div class="pagination" v-if="total>searchParams.limit">
      <a-config-provider :locale="zhCN">
        <a-pagination @change="onChange" :showSizeChanger="false" :current="searchParams.curr" :pageSize="searchParams.limit" :show-total="total => `共计 ${total} 条信息`" show-quick-jumper :total="total" />
      </a-config-provider>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { LeftOutlined } from "@ant-design/icons-vue";
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import logApi from "@/api/log";
import { message } from "ant-design-vue";
const router = useRouter();
// 操作类型
let findAllChildrenList = ref([]);
let date = ref();
let dataList = ref([]);
let searchParams = reactive({
  operateTypeId: "",
  keyWord: "",
  limit: 15,
  curr: 1,
  selectDate: 0,
  beginTime: "",
  endTime: "",
});
const filterDate = ref([
  {
    name: "九十天",
    id: 1,
  },
  {
    name: "近一月",
    id: 2,
  },
  {
    name: "近一周",
    id: 3,
  },
  {
    name: "今日",
    id: 4,
  },
]);
let total = ref(0);
const back = () => {
  router.go(-1);
};

const clearDate = () => {
  searchParams.selectDate = 0;
};
const disabledDate = (time) => {
  // 禁止选择未来日期（可以选择今天）
  return time.getTime() > Date.now();
};
const findAllChildren = async () => {
  const { data, code, msg } = await logApi.findAllChildren();
  if (code == 200) {
    findAllChildrenList.value = data;
  } else {
    message.warning(msg);
  }
};
findAllChildren();
// 查询日志
const searchReport = async () => {
  if(date.value&& date.value.length){
    searchParams.beginTime=dayjs(date.value[0]).format('YYYY-MM-DD 00:00:00')
    searchParams.endTime= dayjs(date.value[1]).format('YYYY-MM-DD 23:59:59')
  }
  const { data, code, msg } = await logApi.searchReport(searchParams);
  if (code == 200) {
    console.log(data);
    total.value = data.count;
    dataList.value = data.data;
  } else {
    message.warning(msg);
  }
};
searchReport();
const currentDate = (val) => {
  date.value = "";
  searchParams.selectDate = val.id;
  searchParams.endTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
  switch (val.id) {
    case 1:
      searchParams.beginTime = dayjs()
        .subtract(90, "days")
        .format("YYYY-MM-DD 00:00:00");
      break;
    case 2:
      searchParams.beginTime = dayjs()
        .subtract(30, "days")
        .format("YYYY-MM-DD 00:00:00");
      break;
    case 3:
      searchParams.beginTime = dayjs()
        .subtract(6, "days")
        .format("YYYY-MM-DD 00:00:00");
      break;
    case 4:
      searchParams.beginTime = dayjs().format("YYYY-MM-DD 00:00:00");
      break;
    default:
      break;
  }
  searchReport();
};
const onChange = (val) => {
  searchParams.curr = val;
  searchReport();
};
const downLoad = ()=>{
  
}


// let convertType = 1;
// let url = "http://************:8090/fcscloud/composite/httpfile"
// let params = {
//    convertType: convertType,
//    fileUrl: "http://************/jchc-oa/mvfile/cloud/" + $("#objectName").val(),
//    fileName:$("input[name=title]").val()+'.docx',
// }
// console.log(path)
// $.ajax({
//    url: path+"/outFile/doPost",//ip地址需要更改为本机地址
//    type: "post",
//    data: JSON.stringify({url: url, params: params}),
//    dataType: "json",
//    contentType: 'application/json;',
//    success: function (res) {
//       window.open(res.value);
//    }
// })
</script>
<style lang="scss">
.log {
  padding: 0 30px;
  height: 100%;
  width: 100%;
  .log_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    border-bottom: 1px dashed #dbdbdb;
    span {
      cursor: pointer;
    }
    .export_btn {
      display: inline-block;
      padding: 0 13px;
      color: #fff;
      height: 28px;
      line-height: 28px;
      background: #3162a7;
      border-radius: 3px;
      text-align: center;
    }
  }
  .search_params {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    .btn {
      height: 34px;
      line-height: 34px;
      text-align: center;
      color: #fff;
      border-radius: 3px;
      background-color: #3161a8;
      padding: 0 18px;
      margin-right: 0;
      border: 1px solid #3161a8;
    }
    .btns {
      height: 34px;
      line-height: 34px;
      text-align: center;
      color: #3161a8 !important;
      background-color: transparent !important;
    }
  }
  .search_params_right {
    display: flex;
    div {
      cursor: pointer;
      margin-left: 10px;
    }
  }
}
.log_table {
  width: 100%;
  table-layout: fixed;
  td,
  th {
    border: 1px solid #bdbdbd;
    text-align: center;
    height: 40px;
    line-height: 40px;
  }
}
.pagination {
  display: flex;
  justify-content: flex-end;
  margin: 19px 0 45px 0;
}
.cell {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  width: 100%;
}
</style>
