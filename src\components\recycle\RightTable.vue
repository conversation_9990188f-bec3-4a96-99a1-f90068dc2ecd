<template>
  <div id="RightTable" class="RightTable" v-if="selectFolder?.uuid" :class="!tableData.length ? 'gray' : ''">
    <div class="buttons">
      <div class="left_btns">
        <span class="btn_bg" v-if="selectFolder?.isAdmin" :class="!isCheck ? 'disabled' : ''" @click="suerDelete">彻底删除</span>
        <div class="btn" :class="!isCheck ? 'disabled' : ''" @click="returnFile">
          <img src="../../assets/img/icon/right-return.png" alt="" />
          还原
        </div>
      </div>
      <div class="right_btns">
        <span class="recycle-tips">提示：回收站不占用网盘空间，文件保存30天后将被自动清除</span>
      </div>
    </div>
    <div class="table">
      <disk-table :editLevel="editLevel" :editDataList="editDataList" :isRecycle="true" @sort="sort" :searchParams="searchParams" :tableData="tableData" :maxHeight="maxHeight" @remove="remove" ref="diskRef"></disk-table>
    </div>
  </div>
  <div class="empty" v-else>
    <div>
      <img src="../../assets/img/background/fload.png" alt="" />
      <span>您还没打开任何文件夹</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { CloseOutlined } from "@ant-design/icons-vue";
import {
  nextTick,
  ref,
  reactive,
  computed,
  onMounted,
  onUnmounted,
  watch,
  inject,
  provide,
} from "vue";
import { ArrowRight } from "@element-plus/icons";
import { Modal } from "ant-design-vue";
import { ElMessage as message } from "element-plus";
import DiskTable from "../table/DiskTableRecycle";
import $ from "jquery";
import hooks from "@/hooks/commonHooks";
import { useStore } from "vuex";
import utils from "@/utils/utils";
import indexApi from "@/api/recycle";
import axios from "axios";
const leftParams = inject("searchParams");
const getUserAuth = inject("getUserAuth");
const store = useStore();
const isAdmin = computed(() => store.state.isAdmin);
// 左侧书本选中
const selectFolder = computed(() => store.state.selectFolder);
// 右侧复选框文件夹选中
const rightSelectFolder = computed(() => store.state.rightSelectFolder);
// 异步加载文件夹
const asyncFolder = computed(() => store.state.asyncFolder);

// 表格组件
const diskRef = ref(null);

let maxHeight = ref(0);

// 获取表格容器的高度
const getTableHeight = () => {
  maxHeight.value = $(".RightTable").height() - 150;
};
// 是否可以操作彻底删除和撤回标签权限
let isCheck = ref(false);
const editLevel = (val) => {
  //isCheck.value = !val.noCheck;
  let hasCheck = diskRef.value.table.filter((item) => {
    return item.check;
  });
  isCheck.value = hasCheck.length ? true : false;
};
let level = ref(0);
let tableData: any = ref([]);

// 右侧表格查询参数
let searchParams = reactive({
  fileNameSort: "ASC",
  totalSpaceSort: "",
  dataCreateTimeSort: "",
  folderId: "",
  bookcaseId: "",
  shelfUserId: "",
});
const find_fileFolders = async (type, fn) => {
  /**
   *1 展开显示
   */
  searchParams.folderId = asyncFolder.value.uuid
    ? asyncFolder.value.uuid
    : selectFolder.value.uuid;
  searchParams.bookcaseId = store.state.bookCase.uuid;
  searchParams.shelfUserId = store.state.selectFile.userId;
  let params = JSON.parse(JSON.stringify(searchParams));
  params = {
    ...params,
    ...leftParams,
  };
  const { data, code, msg } = await indexApi.find_fileFolders(params);
  if (code == 200) {
    switch (type) {
      case 1:
        data.forEach((item) => {
          item.show = true;
        });
        fn(data);
        break;
      default:
        tableData.value = data;
        diskRef.value.initTable(tableData.value);
        break;
    }
  } else {
    message.warning(msg);
  }
};
// 修改数据
/**
 * 1 修改
 * 2 异步新增
 * 3 上传文件
 */
const editDataList = (val, type) => {
  const deep = (table, val) => {
    table.forEach((item) => {
      if (item.uuid == val.uuid) {
        switch (type) {
          case 1:
            item.uuid = val.uuid;
            item.fileName = val.fileName;
            item.type = val.type;
            item.bookcaseId = val.bookcaseId;
            item.parentId = val.parentId;
            item.level = val.level;
            item.isAdd = false;
            item.linkedUser = val.linkedUser;
            item.check = val.check;
            diskRef.value.initTable(tableData.value);
            break;
          case 2:
            find_fileFolders(1, (dataList) => {
              item.children = dataList;
              diskRef.value.initTable(tableData.value);
            });
            break;
          case 3:
            find_fileFolders(1, (dataList) => {
              item.unfold = true;
              fileList.value.forEach((items) => {
                items.show = true;
              });
              item.children = [
                ...JSON.parse(JSON.stringify(fileList.value)),
                ...dataList,
              ];
              diskRef.value.initTable(tableData.value);
            });
            break;
          default:
            break;
        }
      } else if (item.children && item.children.length) {
        deep(item.children, val);
      }
    });
  };
  deep(tableData.value, val);
};

provide("editDataList", editDataList);

// 表格排序
const sort = (val) => {
  switch (val.type) {
    case "fileNameSort":
      searchParams.fileNameSort =
        searchParams.fileNameSort == val.sort ? "" : val.sort;
      searchParams.totalSpaceSort = "";
      searchParams.dataCreateTimeSort = "";
      break;
    case "totalSpaceSort":
      searchParams.totalSpaceSort =
        searchParams.totalSpaceSort == val.sort ? "" : val.sort;
      searchParams.fileNameSort = "";
      searchParams.dataCreateTimeSort = "";
      break;
    case "dataCreateTimeSort":
      searchParams.dataCreateTimeSort =
        searchParams.dataCreateTimeSort == val.sort ? "" : val.sort;
      searchParams.fileNameSort = "";
      searchParams.totalSpaceSort = "";
      break;
    default:
      break;
  }
  store.commit("setClear");
  find_fileFolders();
};
const initRightTable = () => {
  tableData.value = [];
  find_fileFolders();
  nextTick(() => {
    getTableHeight();
    window.addEventListener("resize", () => {
      getTableHeight();
    });
  });
};
watch(selectFolder, (newVal) => {
  if (newVal.uuid) {
    initRightTable();
  }
});
const clearAdd = () => {
  if (!diskRef.value) return;
  let newAdd = diskRef.value.table.filter((item) => {
    return item.isAdd;
  });
  const deepRemove = (val, list) => {
    list.forEach((item, index) => {
      if (item.uuid == val.uuid) {
        list.splice(index, 1);
        diskRef.value.initTable(tableData.value);
      } else if (item.children && item.children.length) {
        deepRemove(val, item.children);
      }
    });
  };
  if (!newAdd.length) return;
  deepRemove(newAdd[0], tableData.value);
};
provide("initRightTable", initRightTable);
provide("clearAdd", clearAdd);
onMounted(() => {
  document.addEventListener("click", (e) => {
    if (
      !$(e.target).closest(".submit,.ant-input,.fileName,.btn,.delete_icon")
        .length
    ) {
      clearAdd();
    }
  });
});

onUnmounted(() => {
  window.removeEventListener("resize", () => {});
  document.removeEventListener("click", () => {});
});
// 彻底删除
const suerDelete = () => {
  if (!isCheck.value) return;
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "确定删除",
    content: () => "确定彻底删除该文件吗?",
    onOk() {
      let deleteList = diskRef.value.table.filter((item) => {
        return item.check;
      });
      indexApi
        .file_handle({
          uuid: deleteList
            .map((item) => {
              return item.uuid;
            })
            .join(),
          dataDelFlag: 2,
        })
        .then((res) => {
          if (res.code == 200) {
            store.commit("setSelectFolder", {});
            deleteList.forEach((item) => {
              remove(item);
            });
            getUserAuth();
          } else {
            message.warning(res.msg);
          }
        });
    },
    onCancel() {},
  });
};
// 撤回
const returnFile = () => {
  if (!isCheck.value) return;
  let deleteList = diskRef.value.table.filter((item) => {
    return item.check;
  });
  indexApi
    .file_handle({
      uuid: deleteList
        .map((item) => {
          return item.uuid;
        })
        .join(),
      dataDelFlag: 0,
    })
    .then((res) => {
      if (res.code == 200) {
        store.commit("setSelectFolder", {});
        deleteList.forEach((item) => {
          remove(item);
        });
        getUserAuth();
      } else {
        message.warning(res.msg);
      }
    });
  return;
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "确定撤回",
    content: () => "确定撤回当前文件吗?",
    onOk() {
      let deleteList = diskRef.value.table.filter((item) => {
        return item.check;
      });
      indexApi
        .file_handle({
          uuid: deleteList
            .map((item) => {
              return item.uuid;
            })
            .join(),
          dataDelFlag: 0,
        })
        .then((res) => {
          if (res.code == 200) {
            store.commit("setSelectFolder", {});
            deleteList.forEach((item) => {
              remove(item);
            });
            getUserAuth();
          } else {
            message.warning(res.msg);
          }
        });
    },
    onCancel() {},
  });
};

const remove = (val) => {
  const deepRemove = (val, list) => {
    list.forEach((item, index) => {
      if (item.uuid == val.uuid) {
        list.splice(index, 1);
        diskRef.value.initTable(tableData.value);
        store.commit("setClear");
      } else if (item.children && item.children.length) {
        deepRemove(val, item.children);
      }
    });
  };
  deepRemove(val, tableData.value);
};
// 移动弹框
const moveParams = reactive({
  // 选中文件
  filterCheck: [],
  visible: false,
});
const move = (val: any) => {
  moveParams.filterCheck = diskRef.value.table.filter((item) => {
    return item.check;
  });
  moveParams.visible = true;
};
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.RightTable {
  width: 40%;
  height: 100%;
  overflow-y: auto;
  padding: 0 10px;
  .buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 25px;
    .left_btns {
      display: flex;
      > span,
      div {
        img {
          margin-right: 3px;
        }
        padding: 0 10px;
      }
    }
    .right_btns {
      .recycle-tips {
        color: #333;
        line-height: 28px;
        font-size: $fontSize;
      }
      display: flex;
      > span,
      div {
        img {
          margin-right: 3px;
        }
        padding: 0 10px;
        &:nth-child(4) {
          width: 82px;
        }
      }
    }
  }
  .breadcrumb {
    margin-top: 18px;
  }
  .table {
    margin-top: 20px;
  }
  .el-table {
    margin-top: 18px;
    border: 1px solid #ebeef5;
    border-bottom: none;
  }
  .el-pagination {
    margin-top: 44px;
    text-align: right;
  }
}
</style>
<style lang="scss">
.gray {
  background-color: #f0f2f5;
  .right_btns > .btn {
    color: #999;
    border: 1px solid #999;
    cursor: not-allowed;
  }
  .el-rate__icon {
    color: #999999 !important;
  }
}
</style>
