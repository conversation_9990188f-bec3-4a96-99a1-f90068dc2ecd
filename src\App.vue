<template>
  <div class="sjk-loading">
    <div class="logo"></div>
  </div>
  <router-view />
  <div class="right_bottom_notification">
    <DownLoad v-if="store.state.file.showDownLoad" :downParams="downParams" @removeDown="removeDown"></DownLoad>
    <div class="uploadContent" v-if="store.state.file.showUpload">
      <div class="uploadLoadingContent">
        <div class="uploadLoading" @click="handleShowFileList">
          <i :class="fileLength ? 'fileNow' : 'fileOk'"></i>
          <div class="title">
            {{ fileLength ? '正在上传' : '上传完成' }}
            <span>剩余{{ fileLength }}项</span>
            <span v-if="fileLength" style="margin-left:17px">预计剩余时间：<span
                style="color:#3161A9">{{ surplusTime }}</span></span>
          </div>
          <CloseOutlined style="color:#999" @click.stop="closeFile" />
        </div>
        <a-progress :percent="allPercentage" />
      </div>

      <ul v-if="showFileList">
        <li v-for="(item, index) in temFileList" :key="item.key">
          <div :key="item.key">
            <span class="icon_file" :style="{ backgroundImage: `url(${getIcon(item.type)}` }"></span>
            <div class="fileName">
              <div class="fileNameTop" :style="{ width: item.percent == 100 ? '250px' : '275px' }">{{ item.name }}</div>
              <div class="size">{{ (item.total / 1024 / 1024).toFixed(2) + 'MB/' + (item.alltotal / 1024 / 1024).toFixed(2) + 'MB' }}<span
                  style="margin-left:20px" v-if="item.percent != 100">上传中</span></div>
            </div>
            <div class="state">
              <CloseOutlined style="color:#999" v-if="item.percent != 100" @click="handleRemove(item, index)" />
              <template v-else>
                <i class="fileOk"></i> 上传完成
              </template>
            </div>
          </div>
          <a-progress :percent="item.percent" :key="item.key" />
        </li>
        <li class="up" @click="handleShowFileList">收起
          <up-outlined />
        </li>
      </ul>
    </div>
    <KeyWords :fileList="fileList" :params="keyWordsParams" @close="closeKeyWords" v-if="keyWordsParams.visible">
    </KeyWords>
  </div>
</template>
<script setup lang="ts">
import { nextTick, onUnmounted, ref, provide, reactive, watch } from "vue";
import { notification, Modal } from "ant-design-vue";
import { CloseOutlined, UpOutlined } from "@ant-design/icons-vue";
import { useRoute } from "vue-router";
import axios from "axios";
import $ from "jquery";
import indexApi from "@/api/Index";
import DownLoad from "@/components/DownLoad.vue";
import UpLoadFile from "@/components/UpLoadFile.vue";
import KeyWords from "@/components/alert/keyWords.vue";
import { useStore } from "vuex";
import utils from "@/utils/utils";
import dayjs from "dayjs";
import hooks from "@/hooks/commonHooks";
const route = useRoute();
// 列表组件选中状态重置
let resetCheck: any = null;
const { acceptList, getIcon } = hooks();
// 请求令牌
const CancelToken = axios.CancelToken;
const store = useStore();
const filte = (item: any) => {
  let newUrl = item[0] + "//" + item[1].split("/")[0];
  return newUrl;
};
const findUrl = async () => {
  const { data, code, msg } = await indexApi.find_url();
  if (code == 200) {
    window.reviewUrl = filte(data.split("//"));
  }
};
findUrl();
const getTime = (time: any) => {
  let secondTime = parseInt(time); // 秒
  let minuteTime = 0; // 分
  if (secondTime >= 60) {
    minuteTime = parseInt(secondTime / 60);
    secondTime = parseInt(secondTime % 60);
    if (minuteTime >= 60) {
      hourTime = parseInt(minuteTime / 60);
      minuteTime = parseInt(minuteTime % 60);
    }
  }
  var result =
    "" +
    (parseInt(secondTime) < 10
      ? "0" + parseInt(secondTime)
      : parseInt(secondTime));

  result =
    "" +
    (parseInt(minuteTime) < 10
      ? "0" + parseInt(minuteTime)
      : parseInt(minuteTime)) +
    ":" +
    result;

  return result;
};
// 关键字弹框
const keyWordsParams = reactive({
  visible: false,
});

// 上传文件列表是否展开
let showFileList = ref(false);
// 上传文件个数
let fileLength = ref(0);
// 已上传文件list
let fileList = ref([]);
// 上传请求存储令牌
let tokenList: any = ref([]);
// 展开上传列表
const handleShowFileList = () => {
  showFileList.value = !showFileList.value;
};
// 剩余时间
let surplusTime = ref();
// 上传总百分比
let allPercentage = ref(0);
// 上传文件预估时间
const progressTime = {
  allTotal: 0,
  //上一次计算时间
  lastTime: 0,
  //上一次计算的文件大小
  lastSize: 0,
  init() {
    this.allTotal = 0;
    this.lastTime = 0;
    this.lastSize = 0;
  },
  progressHandle(event: any) {
    let nowTime = new Date().getTime();
    let intervalTime = (nowTime - this.lastTime) / 1000;
    let lastSize = this.lastSize;
    this.lastSize += event.size;
    let intervalSize = this.lastSize - lastSize;
    this.lastTime = nowTime;
    let speed = intervalSize / intervalTime;
    let bSpeed = 512000;
    surplusTime.value =
      (this.allTotal - this.lastSize) / bSpeed > 0
        ? getTime((this.allTotal - this.lastSize) / bSpeed)
        : getTime(1);
    allPercentage.value =
      (this.lastSize / this.allTotal) * 100 >= 100
        ? 99
        : (this.lastSize / this.allTotal) * 100;
  },
};

const temFileList = ref<any>([]);
let pusTableData: any = null;
let editDataList: any = null;
let getUserAuth: any = null;
// 上传附件
const fileUpload = (val: { files: []; uploadSpace: number }, fns: any) => {
  resetCheck = fns.resetCheck;
  pusTableData = fns.pusTableData;
  editDataList = fns.editDataList;
  getUserAuth = fns.getUserAuth;
  // resetIs = fns.resetIs;
  progressTime.allTotal = val.uploadSpace;
  fileLength.value = val.files.length;
  temFileList.value = [];
  tokenList.value = [];
  for (let i = 0; i < val.files.length; i++) {
    let key = utils.uuid();
    temFileList.value.push({
      name: val.files[i].name,
      percent: 0,
      total: 0,
      lastSize: 0,
      key,
      alltotal: val.files[i].size,
      type: val.files[i].name.split(".")[
        val.files[i].name.split(".").length - 1
      ],
    });
    let formData = new FormData();
    formData.append("filecontent", val.files[i]);
    formData.append("level", "0");
    formData.append("bookcaseId", store.state.file.bookcaseId);
    formData.append("parentId", store.state.file.parentId);
    let token = new CancelToken(function executor(c) {
      tokenList.value.push({ id: key, c });
    });
    // 上传附件回调
    const onUploadProgress = (val: any) => {
      let index = temFileList.value.findIndex((item: any) => item.key == key);
      if (index != -1) {
        temFileList.value[index].lastSize =
          val.loaded - temFileList.value[index].lastSize;
        val.size = temFileList.value[index].lastSize;
        temFileList.value[index].lastSize = val.loaded;
        progressTime.progressHandle(val);
        temFileList.value[index].total = val.loaded;
        temFileList.value[index].percent =
          (val.loaded / val.total) * 100 != 100
            ? (val.loaded / val.total) * 100
            : 99 | 0;
      }
    };

    indexApi
      .fileFolderUpload(formData, token, onUploadProgress)
      .then((res: any) => {
        if (res.code == 200) {
          fileLength.value--;
          res.data.canEdit = true;
          res.data.linkedUser = store.state.file.linkedUser;
          let index = temFileList.value.findIndex((item) => item.key == key);
          if (index !== -1) {
            temFileList.value[index].percent = 100;
          }
          fileList.value.push(res.data);
          if (!fileLength.value) {
            allPercentage.value = 100;
            closeFile();
          }
        }
      });
  }
};
provide("fileUpload", fileUpload);
// 单文件取消上传
const handleRemove = (val: any, index: any) => {
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "终止上传",
    cancelText: () => "取消",
    title: () => "取消上传",
    content: () => "确定要放弃上传吗？",
    onOk() {
      let findToken = tokenList.value.find((item: any) => item.id == val.key);
      findToken.c({ msg: "取消文件上传" });
      progressTime.allTotal =
        progressTime.allTotal - temFileList.value[index].alltotal;
      progressTime.lastSize =
        progressTime.lastSize - temFileList.value[index].lastSize;
      progressTime.progressHandle({ size: 0 });
      temFileList.value.splice(index, 1);
      fileLength.value -= 1;
    },
    onCancel() { },
  });
};

// 重置参数
const resetFileParams = () => {
  temFileList.value = [];
  surplusTime.value = 0;
  showFileList.value = false;
  progressTime.init();
  fileLength.value = 0;
  allPercentage.value = 0;
  fileList.value = [];
  store.commit("file/setUpLoad", { type: 0 });
};

watch(fileLength, (newVal) => {
  if (!newVal) {
    if (
      fileList.value.length &&
      fileList.value.length == temFileList.value.length
    ) {
      allPercentage.value = 100;
      openKeyWords();
    } else {
      resetFileParams();
    }
  }
});
// 上传文件参数重置
const uploadRest = () => {
  tokenList.value.forEach((item: any) => {
    item.c({ msg: "取消文件上传" });
  });
  // 本组件  
  resetFileParams();
  // 子组件 
  // resetIs();
  store.commit('setFileNum')
};
// 打开关键字弹框
const openKeyWords = () => {
  keyWordsParams.visible = true;
};
// 关闭关键字弹框
const closeKeyWords = (val: any) => {
  if (val.keyword) {
    fileList.value.forEach((item: any) => {
      item.keyword = val.keyword;
    });
  }
  const state = store.state;
  if (
    state.selectFolder.uuid == state.file.selectFolder.uuid &&
    route.path == "/index"
  ) {
    if (
      state.rightSelectFolder?.uuid &&
      state.rightSelectFolder.uuid === state.file.rightSelectFolder?.uuid
    ) {
      // 上传到子文件夹
      store.commit("setAsyncFolder", state.rightSelectFolder);
      editDataList(state.rightSelectFolder, 3, fileList.value);
    } else {
      // 上传根目录
      pusTableData(fileList.value);
      resetCheck();
    }
  }
  store.commit("setClear");
  uploadRest();
  keyWordsParams.visible = false;
};
// 全部取消上传
const closeFile = () => {
  if (fileLength.value) {
    Modal.confirm({
      closable: true,
      keyboard: false,
      width: 413,
      class: "alert",
      okText: () => "终止上传",
      cancelText: () => "取消",
      title: () => "取消上传",
      content: () => "列表中有未完成上传的文件，确定要放弃上传吗？",
      onOk() {
        uploadRest();
      },
      onCancel() { },
    });
  } else {
    openKeyWords();
  }
};
// 下载模块
const downParams = reactive({
  time: getTime(0),
  percentage: 0,
});
const downLoadParams: any = {
  lastTime: 0,
  lastSize: 0,
  downTimes: null,
  downtotal: 0,
  // 下载请求token
  downtoken: null,
};
// 下载进度回调函数
const downProgressHandle = (event) => {
  let nowTime = new Date().getTime();
  let intervalTime = (nowTime - downLoadParams.lastTime) / 1000;
  let intervalSize = event.loaded - downLoadParams.lastSize;
  downLoadParams.lastTime = nowTime;
  downLoadParams.lastSize = event.loaded;
  let speed = intervalSize / intervalTime;
  let bSpeed = 512000;
  if (downLoadParams.downTimes) {
    return;
  }
  downLoadParams.downTimes = setTimeout(() => {
    downParams.time = getTime(
      (downLoadParams.downtotal - event.loaded) / bSpeed > 0
        ? (downLoadParams.downtotal - event.loaded) / bSpeed
        : 0
    );
    downLoadParams.downTimes = null;
  }, 1500);
  console.log("下载量", event.loaded, "总量", downLoadParams.downtotal);
  downParams.percentage = (event.loaded / downLoadParams.downtotal) * 100;
};
const onDownloadProgress = (val: any) => {
  downProgressHandle(val);
};
let resetIs = ''
const getFileName = (file: any) => {
  if (file.fileName.endsWith(`.${file.showType}`)) {
    return file.fileName
  } else {
    return `${file.fileName}.${file.showType}`
  }
}
const downLoad = async (val: any) => {
  resetIs = val.resetIs;
  resetCheck = val.resetCheck;
  downLoadParams.downtoken = null;
  const filterCheck = store.state.file.filterCheck;
  // 下载文件大小
  const { data, code, msg } = await indexApi.downloadBatchSpace({
    uuids: filterCheck.map((item: any) => item.uuid),
  });
  if (code == 200) {
    downLoadParams.downtotal = data;
  }

  let token = new CancelToken(function executor(c) {
    downLoadParams.downtoken = c;
  });

  if (
    !filterCheck.filter((item) => item.type == "folder").length &&
    filterCheck.length == 1
  ) {
    let find = filterCheck[0];
    const data = await indexApi.download(
      {
        uuid: find.uuid,
      },
      onDownloadProgress,
      token
    );
    if (!(data instanceof Blob)) return;
    let blob = new Blob([data], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    if (window.navigator.msSaveOrOpenBlob) {
      // 兼容IE
      // navigator.msSaveBlob(blob, find.fileName);
      navigator.msSaveBlob(blob, getFileName(find));
      return;
    }
    console.log(getFileName(find))
    let url = URL.createObjectURL(blob);
    let link = document.createElement("a");
    link.style.display = "none";
    link.href = url;
    link.setAttribute("download", getFileName(find));
    link.click();
    resetCheck();
  } else {
    const data = await indexApi.downloadBatch(
      {
        uuids: filterCheck.map((item) => item.uuid),
      },
      onDownloadProgress,
      token
    );
    if (!(data instanceof Blob)) return;
    let blob = new Blob([data], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    if (window.navigator.msSaveOrOpenBlob) {
      // 兼容IE
      navigator.msSaveBlob(
        blob,
        `dti云空间下载包${dayjs().format("YYYYMMDDHHmmss")}.zip`
      );
      return;
    }
    let url = URL.createObjectURL(blob);
    let link = document.createElement("a");
    link.style.display = "none";
    link.href = url;
    link.setAttribute(
      "download",
      `dti云空间下载包${dayjs().format("YYYYMMDDHHmmss")}.zip`
    );
    link.click();
    resetCheck();
  }

  resetDownParams();
  resetIs();
};
// 终止下载
const removeDown = () => {
  downLoadParams.downtoken && downLoadParams.downtoken({ code: 500 });
  resetDownParams();
  resetCheck();
  resetIs();
};
// 重置下载参数
const resetDownParams = () => {
  downParams.time = 0;
  downParams.percentage = 0;
  downLoadParams.lastTime = 0;
  downLoadParams.lastSize = 0;
  downLoadParams.downtotal = 0;
  downLoadParams.downTimes = null;
  store.commit("file/setDownLoad", 0);
};
provide("downLoad", downLoad);
</script>


<style lang="scss" scoped>
.sjk-loading {
  background-color: rgba(255, 255, 255, 0.38);
  z-index: 999999;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  display: none;
}

.logo {
  width: 90px;
  height: 34px;
  z-index: 999999;
  top: 50%;
  left: 50%;
  margin-top: -17px;
  margin-left: -45px;
  position: fixed;
  background-image: url("./assets/img/logo.png");
  background-size: 100%;
  background-size: 100%;
  animation: revolve 4s linear infinite;
  transform-origin: 50% 50%;
  -webkit-transform-origin: 50% 50%;
  transform: rotate(180deg);
  transition: transform 2s ease 0s;
}

@keyframes revolve {
  0% {
    transform: rotate(0);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@-ms-keyframes revolve {
  0% {
    -ms-transform: rotate(0);
  }

  50% {
    -ms-transform: rotate(180deg);
  }

  100% {
    -ms-transform: rotate(360deg);
  }
}

@-moz-keyframes revolve {
  0% {
    -moz-transform: rotate(0);
  }

  50% {
    -moz-transform: rotate(180deg);
  }

  100% {
    -moz-transform: rotate(360deg);
  }
}

@-webkit-keyframes revolve {
  0% {
    -webkit-transform: rotate(0);
  }

  50% {
    -webkit-transform: rotate(180deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

.right_bottom_notification {
  right: 0px;
  top: auto;
  bottom: 10px;
  position: fixed;
  z-index: 1000;

  .notification_notice {
    position: relative;
    height: 20px;
    width: 384px;
    max-width: calc(100vw - 24px * 2);
    margin-bottom: 16px;
    margin-left: auto;
    padding: 16px 24px;
    overflow: hidden;
    line-height: 1.5715;
    word-wrap: break-word;
    background: #fff;
    border-radius: 2px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
  }
}

$fontSize: 12px;

.uploadContent {
  position: relative;
  width: 392px;

  ul {
    max-height: 70vh;
    overflow: auto;
  }
}

.uploadLoadingContent {
  padding: 0 16px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 5px 14px 0px rgba(60, 60, 60, 0.3);
}

.uploadLoading {
  height: 40px;
  margin-bottom: 10px;
  padding-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  i {
    display: inline-block;
    width: 23px;
    height: 23px;
    background: url("./assets/img/icon/fileOk_icon.png") no-repeat center center;
    background-size: 100% 100%;
  }

  .fileNow {
    background-image: url("./assets/img/icon/fileNow_icon.png");
  }

  .anticon-close {
    margin-right: 14px;
  }

  .title {
    margin-left: 20px;
    font-size: $fontSize;
    flex: 1;

    span {
      color: #999;
    }
  }
}

.gray {
  background-color: #f0f2f5;

  .rate,
  .right_btns>.btn {
    color: #999;
    border: 1px solid #999;
    cursor: not-allowed;
  }

  .el-rate__icon {
    color: #999999 !important;
  }
}

.disabled {
  border: 1px solid #999;
  color: #999 !important;
  cursor: not-allowed !important;
  background-color: #fff !important;

  .el-rate__icon {
    color: #999999 !important;
  }
}

.icon_file {
  display: inline-block;
  width: 22px;
  height: 25px;
  background-size: 100% 100%;
}

.uploadContent {
  ul {
    margin-top: 4px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0px 5px 14px 0px rgba(60, 60, 60, 0.3);
  }

  li {
    background-color: #fff;
    padding: 18px 16px 0 16px;

    >div {
      color: #333333;
      display: flex;

      span {
        margin-right: 10px;
      }

      .fileNameTop {
        width: 275px;
        flex: 0 0 275px;
        overflow: hidden; //溢出隐藏
        white-space: nowrap; //禁止换行
        text-overflow: ellipsis;
      }

      .size {
        color: #999;
      }
    }
  }
}

.up {
  padding: 0 !important;
  text-align: center;
  margin: 10px 0;
  color: #999999;
  cursor: pointer;
}

.fileOk {
  width: 23px;
  height: 23px;
  display: inline-block;
  background-image: url("./assets/img/icon/fileOk_icon.png");
}

.state {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;

  .fileOk {
    width: 18px;
    height: 18px;
    background-size: 100% 100%;
  }
}</style>

<style>.ant-progress-text {
  display: none;
}

.ant-progress-inner {
  margin-top: 0 !important;
}</style>