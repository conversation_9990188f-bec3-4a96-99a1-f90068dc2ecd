export default {
  mounted(el: any) {
    let windows: any = window;
    const content = el.querySelector('.content_slot');
    const dialogHeaderEl = el.querySelector('.title');
    const dragDom = el;
    dialogHeaderEl.style.cssText += ';cursor:move;';
    const sty = (function () {
      if (windows.document.currentStyle) {
        return (dom: any, attr: any) => dom.currentStyle[attr];
      } else {
        return (dom: any, attr: any) => getComputedStyle(dom, '')[attr];
      }
    })();
    dialogHeaderEl.onmousedown = (e: any) => {
      if (content) {
        content.style.display = 'none';
      }
      const disX = e.clientX - dialogHeaderEl.offsetLeft;
      const disY = e.clientY - dialogHeaderEl.offsetTop;
      const screenWidth = document.body.clientWidth;
      const screenHeight = document.documentElement.clientHeight;
      const dragDomWidth = dragDom.offsetWidth;
      const dragDomheight = dragDom.offsetHeight;
      const minDragDomLeft = dragDom.offsetLeft;
      const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth;
      const minDragDomTop = dragDom.offsetTop;
      const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomheight;
      let styL = sty(dragDom, 'left');
      let styT = sty(dragDom, 'top');
      if (styL.includes('%')) {
        styL = +document.body.clientWidth * (+styL.replace(/\%/g, '') / 100);
        styT = +document.body.clientHeight * (+styT.replace(/\%/g, '') / 100);
      } else {
        styL = +styL.replace(/px/g, '');
        styT = +styT.replace(/px/g, '');
      }
      document.onmousemove = function (e) {
        let left = e.clientX - disX;
        let top = e.clientY - disY;
        if (-left > minDragDomLeft) {
          left = -minDragDomLeft;
        } else if (left > maxDragDomLeft) {
          left = maxDragDomLeft;
        }
        if (-top > minDragDomTop) {
          top = -minDragDomTop;
        } else if (top > maxDragDomTop) {
          top = maxDragDomTop;
        }
        dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`;
      };
      document.onmouseup = function (e) {
        if (content) {
          content.style.display = 'block';
        }
        document.onmousemove = null;
        document.onmouseup = null;
      };
    };
  },
};
