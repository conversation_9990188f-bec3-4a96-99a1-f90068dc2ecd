<template>
  <a-modal :maskClosable="false" width="425px" :footer="null" v-model:visible="params.visible" title="配置人员" @cancel="close">
    <div class="content">
      <div></div>
      <div v-for="(item,index) in floorList" :key="index" class="item">
        <div class="label">第{{Arabia_To_SimplifiedChinese(index+1)}}层：</div>
        <div class="right_input">
          <a-input size="small" readonly v-model:value="item.userName" @click.stop="handleOk(item,index)" />
          <ul v-if="item.showUserList" :id="'ztree_'+index" class="ztree">
          </ul>
        </div>
      </div>
      <div class="footer">
      </div>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineProps, nextTick, ref, onUnmounted, onMounted } from "vue";
import commonHooks from "@/hooks/commonHooks";
import { useStore } from "vuex";
import settingApi from "@/api/setting";
import { ElMessage as message } from "element-plus";
const store = useStore();
const { Arabia_To_SimplifiedChinese } = commonHooks();
const { params, shelfListOne,findShelfAdmin } = defineProps({
  params: {
    type: Object,
  },
  shelfListOne: {
    type: Array,
  },
  findShelfAdmin:{
    type:Function
  }
});
let zTree: any = null;
let selectIndex = ref(0);
const floorList: any = ref([]);
// 插入书格主人
const insertShelfAdmin = async () => {
  const { data, code, msg } = await settingApi.insertShelfAdmin(
    floorList.value[selectIndex.value]
  );
  if (code == 200) {
    rest();
  } else {
    message.warning(msg);
  }
};
for (let i = 0; i < shelfListOne?.length; i++) {
  floorList.value.push({
    userId: shelfListOne[i].userId,
    userName: shelfListOne[i].userName,
    bookcaseId: store.state.bookCase.uuid,
    bookcaseNum: i + 1,
    showUserList: false,
  });
}
const rest = () => {
   zTree ? zTree.destroy() :'';
  floorList.value.forEach((item) => {
    item.showUserList = false;
  });
};
onMounted(() => {
  document.addEventListener("click", (e) => {
    if (!$(e.target).closest(".ztree,.a-input").length) {
      rest();
    }
  });
});
onUnmounted(()=>{
  document.removeEventListener('click',()=>{})
})
let ZTreeSetting = {
  async: {
    enable: true,
    autoParam: ["id"],
    otherParam: ["bookcaseId", store.state.bookCase.uuid],
    type: "get",
    url: "/QSecurity/getUserAndDeptForShelf",
    dataFilter: function (treeId, parentNode, res) {
      var childNodes = res.data;
      res.data.forEach((item) => {
        if (floorList.value[selectIndex.value].userId == item.id) {
          item.checked = true;
        }
      });
      return childNodes;
    },
  },
  check: {
    chkboxType: {
      Y: "",
      N: "",
    },
    chkStyle: "radio",
    // radioType: "all",
    enable: true,
  },
  data: {
    simpleData: {
      enable: true,
      idKey: "id",
      pIdKey: "parentId",
      rootPId: "0",
    },
  },
  view: {
    showIcon: false,
  },
  callback: {
    onCheck: function (event, zTreeId, node) {
      if (node.checked) {
        floorList.value[selectIndex.value].userId = node.id;
        floorList.value[selectIndex.value].userName = node.name;
        insertShelfAdmin();
      } else {
        floorList.value[selectIndex.value].userId = "";
        floorList.value[selectIndex.value].userName = "";
        insertShelfAdmin();
      }
    },
  },
};
const handleOk = (item, index) => {
  rest();
  item.showUserList = true;
  selectIndex.value = index;
  nextTick(() => {
    zTree = $.fn.zTree.init($(`#ztree_${index}`), ZTreeSetting);
  });
};
const close = ()=>{
  findShelfAdmin()
}
</script>
<style lang="scss" scoped>
.content {
  padding: 0 27px 32px 31px;
  overflow: auto;
  .item {
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .right_input {
      position: relative;
      height: 27px;
      flex: 1;
      ul {
        position: absolute;
        top: 27px;
        width: 100%;
        height: 80px;
        background-color: #fff;
        z-index: 99;
        border-radius: 3px;
        box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
        overflow: auto;
      }
    }
  }
}
</style>
