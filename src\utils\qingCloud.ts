import { Config, <PERSON>S<PERSON> } from 'qingstor-sdk';

const config = new Config({
  access_key_id: 'RSKBCWLOYAZKGDRBDGQE',
  secret_access_key: '8cwqzDGFz2nuH7ZVgiESebJWNWPN78H7zYwxYd6L',
});

const qingstor = new QingStor(config);

// 查询bucket
export const listBuckets = () => {
  qingstor.listBuckets().then((response: any) => {
    console.log(response.data);
  });
};

// 创建bucket
export const createBucket = () => {
  qingstor
    .Bucket('example-bucket', 'sh1a')
    .put()
    .then((status: any) => {
      // bucket 创建成功，status 应该为 201
      console.log(status);
    })
    .catch((error: any) => {
      // bucket 创建失败，打印返回结果
      console.log(error.response.data);
    });
};

const bucket = new QingStor(config).Bucket('jchc-xxxx', 'sh1a');

// 获取bucket文件列表
export const listObjects = () => {
  // list objects under perfix '/images'
  bucket
    .listObjects({
      limit: 10,
      prefix: '/images',
    })
    .then((response: any) => {
      console.log(response.data);
    })
    .catch((error: any) => {
      console.log(error.response.data);
    });
};

// 上传文件
export const putObject = (file: any) => {
  bucket
    .putObject('/path/to/some_object', {
      'content-type': 'text/html',
      body: file,
    })
    .then((response: any) => {
      // 如果上传成功，得到的 status 应该是 201
      return console.log(response.status);
    })
    .catch((error: any) => {
      return console.log(error);
    });
};

// 删除文件
export const deleteObject = (objectKey: any) => {
  bucket
    .deleteObject(objectKey)
    .then(({ status }: any) => {
      // 对象删除成功，status 应该为 204
      console.log(status);
    })
    .catch((error: any) => {
      // 对象删除失败，打印返回结果
      console.log(error.response.data);
    });
};
// 批量删除指定对象
export const deleteMultipleObjects = (options: any) => {
  bucket
    .deleteMultipleObjects(options)
    .then((response: any) => {
      // 对象删除成功，status 应该为 200
      console.log(response.status);
      // 若 quiet 为 false 请求返回的结果中包含删除成功的对象列表
      console.log(response.data);
    })
    .catch((error: any) => {
      // 对象删除失败，打印返回结果
      console.log(error.response.data);
    });
};
// 列表分段上传
export const listMultipartUploads = (options: any) => {
  bucket
    .listMultipartUploads(options)
    .then((response: any) => {
      console.log(response.data);
    })
    .catch((error: any) => {
      console.log(error);
    });
};
// 访问 Bucket 子资源
export const downloadArrayBuffer = (object_key: any, arraybuffer: any) => {
  const blob = new Blob([arraybuffer], { type: 'text/plain' });

  if ('download' in document.createElement('a')) {
    const link = document.createElement('a');

    link.href = window.URL.createObjectURL(blob);
    link.download = object_key;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();

    window.URL.revokeObjectURL(link.href);
    document.body.removeChild(link);
  } else {
    window.navigator?.msSaveBlob(blob, object_key);
  }
};
export const downLoad = (object_key: any) => {
  bucket
    .getObject(object_key)
    .then((response: any) => {
      downloadArrayBuffer(object_key, response.data);
    })
    .catch((error: any) => {
      console.log(error);
    });
};

// 获取对象元数据
export const headObject = (object_key: any) => {
  bucket
    .headObject(object_key)
    .then((response: any) => {
      // 200, {content-length: "8122", content-type: "text/markdown", last-modified: "Wed, 06 Nov 2019 17:13:35 GMT"}
      console.log(response.status, response.headers);
    })
    .catch((error: any) => {
      console.log(error);
    });
};
