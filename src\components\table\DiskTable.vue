<template>
  <div class="table_header">
    <table :style="{ background: !table.length ? '#fff' : '' }">
      <colgroup>
        <col />
        <col width="100px" />
        <col width="70px" />
        <col width="100px" />
        <!-- <col width="100px" /> -->
      </colgroup>
      <tr>
        <th>
          <div
            class="cell"
            style="justify-content: flex-start; margin-left: 39px"
          >
            <el-checkbox
              style="margin-right: 20px"
              v-model="checkAll"
              @change="
                (val) => {
                  handleSelect(0, val);
                }
              "
            ></el-checkbox>
            文件名
            <span class="caret-wrapper">
              <i
                class="sort-caret ascending"
                @click="listSort({ type: 'fileNameSort', sort: 'ASC' })"
                :class="
                  upParams?.fileNameSort == 'ASC' ? 'ascending_active' : ''
                "
              ></i>
              <i
                class="sort-caret descending"
                @click="listSort({ type: 'fileNameSort', sort: 'DESC' })"
                :class="
                  upParams?.fileNameSort == 'DESC' ? 'descending_active' : ''
                "
              ></i>
            </span>
          </div>
        </th>
        <th>标签</th>
        <th>
          <div class="cell">
            大小
            <span class="caret-wrapper">
              <i
                class="sort-caret ascending"
                @click="listSort({ type: 'totalSpaceSort', sort: 'ASC' })"
                :class="
                  upParams?.totalSpaceSort == 'ASC' ? 'ascending_active' : ''
                "
              ></i>
              <i
                class="sort-caret descending"
                @click="listSort({ type: 'totalSpaceSort', sort: 'DESC' })"
                :class="
                  upParams?.totalSpaceSort == 'DESC' ? 'descending_active' : ''
                "
              ></i>
            </span>
          </div>
        </th>
        <th>
          <div class="cell">
            上传时间
            <span class="caret-wrapper">
              <i
                class="sort-caret ascending"
                @click="listSort({ type: 'dataCreateTimeSort', sort: 'ASC' })"
                :class="
                  upParams?.dataCreateTimeSort == 'ASC'
                    ? 'ascending_active'
                    : ''
                "
              ></i>
              <i
                class="sort-caret descending"
                @click="listSort({ type: 'dataCreateTimeSort', sort: 'DESC' })"
                :class="
                  upParams?.dataCreateTimeSort == 'DESC'
                    ? 'descending_active'
                    : ''
                "
              ></i>
            </span>
          </div>
        </th>
        <!-- <th>查看人员</th> -->
      </tr>
    </table>
  </div>
  <div class="table_body" :style="{ height: maxHeight + 'px' }">
    <div class="empty" style="width: 100%" v-if="!table.length">
      <div>
        <img src="../../assets/img/background/fload.png" alt="" />
        <span>您还没上传任何文件</span>
      </div>
    </div>
    <table class="title" v-else>
      <colgroup>
        <col />
        <col width="100px" />
        <col width="70px" />
        <col width="100px" />
        <!-- <col width="100px" /> -->
      </colgroup>
      <template v-for="(item, index) in table" :key="item.uuid">
        <tr
          @dblclick="openEditInfo(item)"
          :class="item.check && item.canEdit ? 'active_bg' : 'no_bg'"
          :style="{ backgroundColor: item.canEdit ? '#E5F7FF' : '' }"
          v-if="item.show"
          :data-key="item.rowKey"
          @mouseenter="showClassSetting(item, $event)"
        >
          <td>
            <div class="td_cell">
              <span :style="{ paddingLeft: item.leave * 15 + 'px' }"></span>
              <i
                :style="{
                  visibility:
                    item.type === 'folder' && !item.isAdd
                      ? 'visible'
                      : 'hidden',
                }"
                @click="showTR(item)"
                :class="item.unfold ? 'icon_hide' : 'icon_show'"
              ></i>
              <!-- || item.type=='folder' ? 'visible' : 'hidden' -->
              <!-- item.canEdit && -->
              <el-checkbox
                @change="handleSelect(1, item)"
                :style="{
                  visibility:
                    (item.canEdit || item.type == 'folder') && !item.isAdd
                      ? 'visible'
                      : 'hidden',
                }"
                v-model="item.check"
              ></el-checkbox>
              <!-- 文件夹图标 -->
              <span class="icon_common" v-if="item.type === 'folder'"></span>
              <span
                class="icon_file"
                v-if="item.type !== 'folder'"
                :style="{ backgroundImage: `url(${getIcon(item.showType)}` }"
              ></span>
              <!-- 文件名  -->
              <el-tooltip
                popper-class="fileInfoTip"
                :placement="item.isBottom ? 'bottom-start' : 'top-start'"
                effect="light"
                v-if="item.type === 'file' && !item.isEdit"
              >
                <template #content>
                  <div class="tip_content">
                    <div>{{ getFileName(item) }}</div>
                    <div>
                      <span class="label">
                        <span class="label_title">文件类型：</span
                        >{{ item.showType }}
                      </span>
                    </div>
                    <div v-if="false">
                      <span class="label">
                        <span class="label_title">所属文件夹：</span
                        >{{ selectFolder.fileName }}
                      </span>
                    </div>
                    <div>
                      <span class="label">
                        <span class="label_title">关键词：</span>
                        {{ item.keyword }}</span
                      >
                    </div>
                    <div>
                      <span class="label"
                        ><span class="label_title">查看人员：</span
                        ><span v-html="format(item, 2)"></span
                      ></span>
                    </div>
                  </div>
                </template>
                <!-- item.fileName.lastIndexOf(".")!=-1? item.fileName.substring(0,item.fileName.lastIndexOf(".")) : item.fileName -->
                <!-- @dblclick="openEditName(item)" -->
                <span class="fileName" @click="handleOpenDialog(item)">{{
                  getFileName(item)
                }}</span>
              </el-tooltip>
              <!-- 文件夹名 -->
              <span
                class="fileName"
                v-if="item.type === 'folder' && !item.isAdd && !item.isEdit"
                @dblclick="openEditName(item)"
                >{{ item.fileName }}</span
              >
              <a-input
                id="fileNameInput"
                @keyup.enter="addFload(item)"
                :maxlength="50"
                v-model:value="newFileName"
                size="small"
                style="width: 160px"
                v-if="item.isAdd || item.isEdit"
              >
                <template #suffix>
                  <span class="delete_icon" @click="clearFileName"></span>
                </template>
              </a-input>
              <!-- 保存  -->
              <span
                class="submit"
                v-if="item.isAdd || item.isEdit"
                @click="addFload(item)"
              ></span>
            </div>
          </td>
          <td>
            <a-rate
              v-if="item.type === 'file'"
              @change="upDataLeve(item)"
              v-model:value="item.level"
              :count="3"
            ></a-rate>
          </td>
          <td>
            <span class="size" v-if="item.type === 'file'">
              <el-tooltip
                v-if="item.type === 'file'"
                :content="renderSize(item.totalSpace, 1)"
                placement="bottom"
                effect="light"
              >
                <span>{{ renderSize(item.totalSpace, 0) }}</span>
              </el-tooltip>
              <a-tooltip
                v-if="false"
                :placement="item.isBottom ? 'bottom' : 'top'"
                color="#fff"
                :getPopupContainer="
                  (e) => {
                    return e;
                  }
                "
              >
                <template #title>
                  <ul class="more">
                    <li @click="downLoad(item)">下载</li>
                    <li @click="remove(item)">删除</li>
                    <li @click="move(item)">移动</li>
                    <li @click="openNewLabel(item)">标签</li>
                    <li @click="openEditKeyword(item)">关键词</li>
                  </ul>
                </template>
                <div class="more_icon">
                  <img src="../../assets/img/icon/dot.png" />
                </div>
              </a-tooltip>
            </span>
          </td>
          <td>
            <el-tooltip
              v-if="item.type === 'file'"
              :content="item.dataCreateTime"
              placement="bottom"
              effect="light"
            >
              <span style="margin-right: 5px">{{
                getDateDiff(dayjs(item.dataCreateTime).format("YYYY-MM-DD"))
              }}</span>
            </el-tooltip>
            <span
              v-if="item.type !== 'file' && !item.isAdd && item.canEdit"
              title="新建下级文件夹"
              class="add_flod"
              @click="addFold(item)"
            ></span>
          </td>
          <td @dblclick="settingUser(item)" v-if="false">
            <el-tooltip
              v-if="item.type === 'file'"
              :content="format(item, 1)"
              :placement="item.isBottom ? 'bottom' : 'top'"
              effect="light"
            >
              <span
                class="inline_type"
                :style="{
                  cursor: item.canEdit && !item.isAdd ? 'pointer' : 'default',
                }"
                v-html="format(item, 2)"
              ></span>
            </el-tooltip>
          </td>
        </tr>
      </template>
    </table>
  </div>
  <newLabel
    v-if="newLabelParams.visible"
    :params="newLabelParams"
    @close="closeNewLabel"
  ></newLabel>
  <moveLog
    v-if="moveParams.visible"
    :params="moveParams"
    @close="closeMoveLog"
  ></moveLog>
  <SettingUser
    :type="5"
    v-if="settingUserParams.visible"
    :params="settingUserParams"
    @close="closeSettingUser"
  ></SettingUser>
  <Dialog
    ref="showAssociatedAttachment"
    :width="dialogOption.dialogWidth"
    :height="dialogOption.dialogHeight"
    :left="dialogOption.dialogLeft"
    :top="dialogOption.dialogTop"
    title="文件预览"
  >
    <PreviewDocxVue :src="showUrl"></PreviewDocxVue>
  </Dialog>
  <editKeyWordsVue
    v-if="editKeyWordsParams.visible"
    :params="editKeyWordsParams"
    @close="closeKeyWords"
  ></editKeyWordsVue>
  <editFileSetVue
    v-if="editFileSetParams.visible"
    :params="editFileSetParams"
    @close="closeFileSetParams"
  ></editFileSetVue>
  <alertFold
    v-if="addFoldParams.visible"
    :params="addFoldParams"
    @close="initFolder"
  ></alertFold>
</template>
<script lang="ts" setup>
import {
  ref,
  defineProps,
  watch,
  nextTick,
  defineEmits,
  reactive,
  defineExpose,
  computed,
  inject,
  onMounted,
  onUnmounted,
} from "vue";
import { Modal } from "ant-design-vue";
import { ElMessage as message } from "element-plus";

import commonHooks from "@/hooks/commonHooks";
import moveLog from "../alert/move.vue";
import newLabel from "../alert/newLabel.vue";
import indexApi from "@/api/Index";
import SettingUser from "../rightsProfile/SettingUser.vue";
import { useStore } from "vuex";
import Dialog from "@/components/Dialog.vue";
import PreviewDocxVue from "../PreviewDocx.vue";
import editKeyWordsVue from "../alert/editKeyWords.vue";
import editFileSetVue from "../alert/editFileSet.vue";
import alertFold from "@/components/alert/addFold.vue";
import dayjs from "dayjs";
const store = useStore();
const { getDateDiff, getIcon, renderSize, acceptList } = commonHooks();
const props = defineProps({
  tableData: {
    type: Array,
    default: [],
  },
  maxHeight: {
    type: Number,
  },
  searchParams: {
    type: Object,
  },
  editDataList: {
    type: Function,
  },
  editLevel: {
    type: Function,
  },
  isRecycle: {
    type: Boolean,
    default: false,
  },
});
const getUserAuth = inject("getUserAuth");

// 修改文件名
const getFileName = (file: any) => {
  if (file.fileName.endsWith(`.${file.showType}`)) {
    return file.fileName.substring(
      0,
      file.fileName.lastIndexOf(`.${file.showType}`)
    );
  } else {
    return file.fileName;
  }
};

let timer = reactive({
  clickCount: 0,
  time: 0,
});
// 弹出框配置
const dialogOption = reactive({
  dialogWidth: "60%",
  dialogHeight: "100%",
  dialogLeft: "0",
  dialogTop: "0",
});
// 预览附件弹框
let showAssociatedAttachment = ref();
let showUrl = ref();
let upParams = ref();
let isClick: any = null;
let times: any = null;
// 预览
const handleOpenDialog = (val) => {
  if (times) {
    clearTimeout(times);
  }
  times = setTimeout(() => {
    if (val.type == "file" && !isRecycle) {
      if (!acceptList.value.includes(val.showType.toLowerCase()))
        return message.warning("该文件类型不支持预览!");
      // let url = "https://sjkyzyl.jchc.cn/fcscloud/composite/httpfile";
      // indexApi
      //   .doPost({
      //     url,
      //     params: {
      //       convertType: 1,
      //       fileUrl:
      //         window.reviewUrl +
      //         `/jchc-edisk/file-preview/cloud?objectName=${val.objectName}&uuid=${val.uuid}`,
      //       fileName: val.fileName,
      //     },
      //   })
      //   .then((res) => {
      //     if (res.code == 200 && res.data) {
      //       showUrl.value = res.data;
      //       showAssociatedAttachment.value.visible = true;
      //     } else {
      //       message.warning("文件正在转换中...");
      //     }
      //   });
      indexApi.getPrevie({ uuid: val.uuid }).then((res) => {
        if (res.code == 200) {
          if (res.data.idocviewUuid) {
            showUrl.value =
              "https://sjkwd.jchc.cn/view/" + res.data.idocviewUuid;
            showAssociatedAttachment.value.visible = true;
          } else {
            message.warning("文件正在转换中...");
          }
        }
      });
    }
  }, 300);
};

watch(
  () => props.searchParams,
  (newVal, oldVal) => {
    upParams.value = JSON.parse(JSON.stringify(newVal));
  },
  {
    deep: true,
    immediate: true,
  }
);
// 配置人员参数
let settingUserParams = reactive({
  visible: false,
  title: "权限配置",
  alert: "请配置文件的查看权限",
  // 书本id
  uuid: "",
  // 选中文件
  filterCheck: [],
});
let checkAll = ref(false);
const settingUser = (val) => {
  if (!val.canEdit) return;
  if (isRecycle) {
    return;
  }
  settingUserParams.filterCheck = [val];
  settingUserParams.uuid = selectFolder.value.uuid;
  settingUserParams.visible = true;
};
const closeSettingUser = () => {
  settingUserParams.visible = false;
};
const emit = defineEmits(["remove", "sort"]);
// 左侧书本选中
const selectFolder = computed(() => store.state.selectFolder);
// 右侧文件夹选中
const rightSelectFolder = computed(() => store.state.rightSelectFolder);
let clientHeight = ref(0);
let newFileName = ref("");
nextTick(() => {
  clientHeight.value = document.body.clientHeight;
});
const tableDataValue = inject("tableDataValue");
// 新数据
let table: any = ref([]);
// 备份新数据
let copyTable: any = ref([]);
// 递归原数据将所有子节点全部放在一级
const deep = (val: any, parentId: string, level = 1, parentIndex: number) => {
  val.forEach((item: any, index: number) => {
    // 是有否此节点
    let oldRow = copyTable.value.find((items: any) => {
      return items.uuid == item.uuid;
    });
    // 节点显示/隐藏
    // 如果没有父节点默认就展示
    if (parentId) {
      item.parentId = parentId;
      // 判断数据本身有无节点
      if (item.hasOwnProperty("show")) {
        item.show = item.show;
      } else if (oldRow && oldRow.hasOwnProperty("show")) {
        // 判断缓存是 有使用缓存
        item.show = oldRow.show;
      } else {
        item.show = true;
      }
    } else {
      item.show = true;
    }

    // 节点层级
    item.leave = level;
    // 选中状态
    // item.check = oldRow ? oldRow.check : false;
    item.check = false;
    // 节点标识
    item.rowKey =
      (parentIndex ? parentIndex + "_" : "") +
      (index + 1 < 10 ? "0" : "") +
      (index + 1);
    // 子节点关闭/展开

    // 判断当前是否有改属性
    if (item.hasOwnProperty("unfold")) {
      item.unfold = item.unfold;
    } else if (oldRow && oldRow.hasOwnProperty("unfold")) {
      item.unfold = oldRow.unfold;
    } else {
      item.unfold = false;
    }
    // 是否编辑
    item.edit = false;
    table.value.push(item);
    if (item.children && item.children.length) {
      item.isParent = true;
      deep(item.children, item.uuid, level + 1, item.rowKey);
    } else {
      item.isParent = false;
    }
  });
};
const resetOld = () => {
  table.value = [];
  copyTable.value = [];
};
const initTable = (newVal) => {
  let tableList = JSON.parse(JSON.stringify(newVal));
  copyTable.value = JSON.parse(JSON.stringify(table.value));
  checkAll.value = false;
  table.value = [];
  deep(tableList);
  // 初始化完毕要把父级数据的unfold和show状态删除 ！！！
  tableDataValue();
  nextTick(() => {
    $("#fileNameInput").focus();
  });
};
// 展开/关闭子节点
const showTR = (val: any, type) => {
  type ? "" : (val.unfold = !val.unfold);
  if (val.unfold || type) {
    if (type == 1) {
      let items = table.value.find((item) => {
        return item.uuid == val.uuid;
      });
      items.unfold = true;
    }
    store.commit("setAsyncFolder", val);
    table.value.forEach((item: any) => {
      if (item.parentId && item.parentId == val.uuid) {
        item.show = val.unfold;
      }
    });
    copyTable.value = JSON.parse(JSON.stringify(table.value));
    editDataList(val, 2);
  } else {
    table.value.forEach((item: any) => {
      if (
        item.rowKey.indexOf(val.rowKey) === 0 &&
        item.rowKey.length > val.rowKey.length
      ) {
        item.unfold = false;
        item.show = false;
      }
    });
  }
};
// 全选/反选
const handleSelect = (type, val: any) => {
  if (!type) {
    table.value.forEach((item) => {
      if (item.canEdit || item.type == "folder") {
        item.check = val;
      }
    });
  }
  let selectArr = table.value.filter((item) => {
    if (item.canEdit || item.type == "folder") {
      return item.check == true;
    }
  });

  if (
    selectArr.length ==
      table.value.filter((item) => {
        return item.canEdit || item.type == "folder";
      }).length &&
    table.value.filter((item) => {
      return item.canEdit || item.type == "folder";
    }).length
  ) {
    checkAll.value = true;
  } else {
    checkAll.value = false;
  }
  store.commit("setRightSelectFolder", {});
  if (val.type != "file") {
    // 选择文件夹则取消子文件夹包括同级父文件夹选中
    if (val.check) {
      table.value.forEach((item: any) => {
        if (
          (item.rowKey.indexOf(val.rowKey) == 0 ||
            val.rowKey.indexOf(item.rowKey) === 0) &&
          item.rowKey.length != val.rowKey.length
        ) {
          item.check = false;
        }
      });
    }
  }
  props.editLevel();
  // if (val.type != "file") {
  //   store.commit("setRightSelectFolder", val);
  //   // 将所有该节点下的子节点全选
  //   table.value.forEach((item: any) => {
  //     if (item.rowKey.indexOf(val.rowKey) == 0) {
  //       item.check = val.check;
  //     }
  //   });
  //   const deepData = (val) => {
  //     //   父节点
  //     let parent = table.value.find((item: any) => {
  //       return item.uuid == val.parentId;
  //     });
  //     if (parent) {
  //       //   所有的兄弟节点
  //       let arr = table.value.filter((item: any) => {
  //         if (item.parentId == val.parentId) {
  //           return item;
  //         }
  //       });
  //       // 未选中的兄弟节点
  //       let index = arr.findIndex((item: any) => {
  //         return !item.check;
  //       });
  //       if (index == -1) {
  //         parent.check = true;
  //         deepData(parent);
  //       } else {
  //         parent.check = false;
  //         table.value.forEach((item: any) => {
  //           if (
  //             val.rowKey.indexOf(item.rowKey) == 0 &&
  //             val.rowKey.length != item.rowKey.length
  //           ) {
  //             item.check = false;
  //           }
  //         });
  //       }
  //     }
  //   };
  //   deepData(val);
  // }
};
// 文件详细操作展示位置
const showClassSetting = (val: any, e: any) => {
  let height = clientHeight.value - $(e.target).offset().top;
  height < 220 ? (val.isBottom = false) : (val.isBottom = true);
};
// 删除文件
const remove = (val: any) => {
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "确定删除",
    content: () => "确定删除该文件吗?删除后在30天内可以通过回收站还原",
    onOk() {
      indexApi.fileFolderDelete({ uuid: val.uuid }).then((res) => {
        if (res.code == 200) {
          emit("remove", val);
        } else {
          message.warning(res.msg);
        }
      });
    },
    onCancel() {},
  });
};
// 向外暴露方法
defineExpose({ table, handleSelect, initTable, resetOld, checkAll });
// 移动弹框
const moveParams = reactive({
  // 选中文件
  filterCheck: [],
  visible: false,
});
const initRightTable = inject("initRightTable");
const closeMoveLog = (val: any) => {
  resetOld();
  store.commit("setClear");
  initRightTable();
  moveParams.visible = false;
};
const move = (val: any) => {
  moveParams.filterCheck[0] = val;
  moveParams.visible = true;
};
// 标签弹框数据
const newLabelParams = reactive({
  visible: false,
  uuid: "",
  level: "",
  fileName: "",
  bookcaseId: "",
  type: "",
  parentId: "",
  totalSpace: "",
  linkedUser: [],
  canEdit: true,
});
const openNewLabel = (item) => {
  newLabelParams.uuid = item.uuid;
  newLabelParams.level = item.level;
  newLabelParams.fileName = item.fileName;
  newLabelParams.bookcaseId = item.bookcaseId;
  newLabelParams.parentId = item.parentId;
  newLabelParams.type = item.type;
  newLabelParams.linkedUser = item.linkedUser;
  newLabelParams.totalSpace = item.totalSpace;
  newLabelParams.visible = true;
};
const closeNewLabel = () => {
  newLabelParams.visible = false;
};
// 数据排序
const listSort = (val: any) => {
  resetOld();
  emit("sort", val);
};
// 保存文件夹
const addFload = async (val) => {
  let params = {
    uuid: val.uuid,
    fileName: newFileName.value,
    type: val.type,
    bookcaseId: store.state.selectFile.bookcaseId,
    parentId: val.parentId,
    level: val.level | 0,
  };
  if (!newFileName.value) return message.warning("名称不能为空!");
  let yzparams = {
    parentId: val.parentId,
    fileName: newFileName.value,
  };
  if (!val.isAdd) {
    params = {};
    params.uuid = val.uuid;
    params.fileName = newFileName.value;
    yzparams.uuid = val.uuid;
  }

  const res = await indexApi.checkName(yzparams);
  if (res.code == 200) {
    console.log(res);
    if (res.data != 1) {
      const { data, code, msg } = await indexApi[
        val.isAdd ? "fileFolderAdd" : "fileFolderUpdate"
      ](params);
      if (code == 200) {
        val.isAdd = false;
        val.isEdit = false;
        val.fileName = newFileName.value;
        val.canEdit = true;
        newFileName.value = "";
        copyTable.value = JSON.parse(JSON.stringify(table.value));
        props.editDataList(val, 1);
        getUserAuth();
      } else {
        message.warning(msg);
      }
    } else {
      message.warning(res.msg);
    }
  }
};
const clearAdd = inject("clearAdd");
// 修改文件夹名称
const openEditName = (val) => {
  clearTimeout(timer.time);
  if (props.isRecycle) {
    return false;
  }
  let newAdd = table.value.filter((item) => {
    return item.isAdd;
  });
  if (newAdd.length) {
    clearAdd();
  }
  table.value.forEach((item) => {
    item.isEdit = false;
  });
  newFileName.value = val.fileName;
  val.isEdit = true;
};
// 清空文件夹名称
const clearFileName = () => {
  newFileName.value = "";
};
// 查看人员名称格式化
const format = (val, type) => {
  let str = `${val.userName}`;
  let html = `<span class="font">${val.userName}</span>`;
  val.linkedUser.forEach((item) => {
    str += `、${item.name}`;
    html += `<span>、${item.name}</span>`;
  });
  if (type == 1) {
    return str;
  } else {
    return html;
  }
};
// 下载单个
const downLoad = async (val) => {
  window.location.href = "/jchc-edisk/fileFolder/download/" + val.uuid;
};
const editKeyWordsParams = reactive({
  visible: false,
  uuid: "",
  level: "",
  fileName: "",
  bookcaseId: "",
  type: "",
  parentId: "",
  totalSpace: "",
  linkedUser: [],
  canEdit: true,
});
const openEditKeyword = (val) => {
  Object.keys(val).forEach((item) => {
    editFileSetParams[item] = val[item];
  });
  editKeyWordsParams.visible = true;
};
const closeKeyWords = () => {
  editKeyWordsParams.visible = false;
};

/**
 * 修改文件配置
 */
const editFileSetParams: any = reactive({
  visible: false,
});
// 修改信息
const openEditInfo = (val) => {
  if (val.type == "folder" || !val.canEdit) return;
  if (times) {
    clearTimeout(times);
  }
  Object.keys(val).forEach((item) => {
    editFileSetParams[item] = val[item];
  });
  // editFileSetParams.fileName =
  //   val.fileName.indexOf(".") != -1
  //     ? val.fileName.substring(0, val.fileName.indexOf("."))
  //     : val.fileName;
  editFileSetParams.fileName = getFileName(val);
  editFileSetParams.visible = true;
};

const closeFileSetParams = () => {
  editFileSetParams.visible = false;
};
const addNewFolder = inject("addNewFolder");

const addFoldParams = reactive({
  visible: false,
});

const addFold = (val) => {
  let index = table.value.findIndex((item) => item.isAdd);
  if (index != -1) return;
  addNewFolder(2, val.uuid);
};

const initFolder = (val) => {
  addFoldParams.visible = false;

  if (val.isAdd == 1) {
    getUserAuth();
    showTR(val.params, 1);
  }
};

const upDataLeve = async (val) => {
  const { data, code, msg } = await indexApi.userLevel({
    level: val.level,
    uuid: val.uuid,
  });
  if (code == 200) {
    copyTable.value = JSON.parse(JSON.stringify(table.value));
    props.editDataList(val, 1);
  }
};

onMounted(() => {
  document.addEventListener("click", (e) => {
    if (
      !$(e.target).closest(".submit,.ant-input,.fileName,.btn,.delete_icon")
        .length
    ) {
      newFileName.value = "";
      table.value.forEach((item) => {
        item.isEdit = false;
      });
    }
  });
});
onUnmounted(() => {
  window.removeEventListener("resize", () => {});
  document.removeEventListener("click", () => {});
});
</script>

<style lang="scss" scoped>
$fontSize: 12px;
.label {
  display: flex;
}
.label_title {
  display: inline-block;
  width: 60px;
  flex: 0 0 60px;
  text-align: right;
}
.fileName {
  display: inline-block;
  flex: 0 0 80%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  cursor: pointer;
}
.table_header {
  min-width: 100%;
  table {
    width: 100%;
  }
  tr {
    border-top: 1px solid #e0e8f2;
  }
}
.table_body {
  min-width: 100%;
  overflow: auto;
  table {
    width: 100%;
    .no_bg {
      &:hover {
        border-left: 1px solid #107df8;
        border-right: 1px solid #107df8;
        td {
          border-bottom: 1px solid #107df8;
          border-top: 1px solid #107df8;
        }
      }
    }
  }
}
table {
  table-layout: fixed;
  cursor: default;
  tr {
    height: 32px;
    border: 1px solid #e0e8f2;
    &:hover {
      .size {
        .more_icon {
          display: inline-block;
        }
      }
    }
    td,
    th {
      font-size: $fontSize;
      text-align: center;
    }
  }
}

.table_body i {
  display: inline-block;
  flex: 0 0 14px;
  height: 14px;
  background: url("../../assets/img/icon/table_hide.png") no-repeat center
    center;
  background-size: 100% 100%;
}

.td_cell {
  display: flex;
  align-items: center;
  i {
    margin-right: 10px;
  }
}
.el-checkbox {
  margin-right: 10px;
}
.inline_type {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;

  -webkit-touch-callout: none; /* iOS Safari */

  -webkit-user-select: none; /* Chrome/Safari/Opera */

  -khtml-user-select: none; /* Konqueror */

  -moz-user-select: none; /* Firefox */

  -ms-user-select: none; /* Internet Explorer/Edge */

  user-select: none; /* Non-prefixed version, currently

not supported by any browser */
}
.more {
  width: 60px;
  li {
    font-size: $fontSize;
    line-height: 28px;
    text-align: center;
    cursor: pointer;
    color: #000;
    &:hover {
      color: #3162a7;
    }
  }
}
.size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  span {
    margin-right: 30px;
  }
  .more_icon {
    height: 30px;
    line-height: 28px;
    display: none;
  }
}
.empty {
  div {
    top: 50% !important;
    transform: translate(-50%, -50%);
  }
}
.table_header {
  .cell {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    .caret-wrapper {
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      height: 20px;
      width: 24px;
      vertical-align: middle;
      cursor: pointer;
      overflow: initial;
      position: relative;
    }
    .sort-caret {
      width: 0;
      height: 0;
      border: solid 5px transparent;
      position: absolute;
      left: 7px;
    }
    .ascending {
      border-bottom-color: #999;
    }
    .ascending_active {
      border-bottom-color: #3162a7 !important;
    }
    .descending {
      bottom: -3px;
      border-top-color: #999;
    }
    .descending_active {
      border-top-color: #3162a7 !important;
    }
  }
}
.icon_hide {
  background: url("../../assets/img/icon/table_hide.png") no-repeat center
    center !important;
  cursor: pointer;
}
.icon_show {
  cursor: pointer;
  background: url("../../assets/img/icon/table_show.png") no-repeat center
    center !important;
}
.icon_common {
  display: inline-block;
  flex: 0 0 19px;
  height: 15px;
  margin: 0 10px;
  background: url("../../assets/img/icon/folder_table_icon.png") no-repeat
    center center;
}
.icon_file {
  display: inline-block;
  flex: 0 0 19px;
  height: 15px;
  margin: 0 10px;
  background: url("../../assets/img/icon/other_table_icon.png") no-repeat center
    center;
  background-size: 75% 100%;
}
.delete_icon {
  display: inline-block;
  width: 13px;
  height: 13px;
  background: url("../../assets/img/icon/delete_table_icon.png") no-repeat
    center center;
  cursor: pointer;
  flex: 0 0 13px;
}
.submit {
  display: inline-block;
  width: 22px;
  height: 22px;
  background: url("../../assets/img/icon/submit_icon.png") no-repeat center
    center;
  cursor: pointer;
  margin-left: 10px;
}
.el-checkbox {
  height: 32px !important;
}
.add_flod {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("../../assets/img/icon/add_icon_cl.png") no-repeat center
    center;
  background-size: 100% 100%;
  cursor: pointer;
}
</style>
<style lang="scss">
.table_body .el-checkbox__inner {
  width: 15px !important;
  height: 15px !important;
}
.font {
  font-weight: bold !important;
}
.active_bg {
  background-color: #3162a7 !important;
  span {
    color: #fff !important;
  }
}
</style>
