export default {
  namespaced: true,
  state: {
    // 上传显示
    showUpload: false,
    // 选中文件
    files: [],
    // 上传文件数量
    fileLength: 0,
    // 选中文件大小
    allTotal: 0,
    // 上传文件书柜id
    bookcaseId: '',
    // 上传父级id
    parentId: '',
    rightSelectFolder: {},
    // 上传选中文件夹
    selectFolder: {},
    // 下载显示
    showDownLoad: false,
    // 选中下载
    filterCheck: [],
    // 上传文件权限 
    linkedUser:[]
  },
  mutations: {
    setDownLoad(state: any, val: any) {
      if (val.type == 1) {
        state.showDownLoad = true;
        state.filterCheck = val.filterCheck;
      } else {
        state.showDownLoad = false;
        state.filterCheck = [];
      }
    },
    setUpLoad(state: any, val: any) {
      if (val.type == 1) {
        state.files = val.files;
        console.log(state.files);
        state.allTotal = val.uploadSpace;
        state.bookcaseId = val.bookcaseId;
        state.parentId = val.parentId;
        state.rightSelectFolder = val.rightSelectFolder;
        state.selectFolder =val.selectFolder
        state.showUpload = true;
      } else {
        state.files = 0;
        console.log(state.files);
        state.allTotal = 0;
        state.bookcaseId = '';
        state.parentId = '';
        // state.rightSelectFolder = {};
        // state.selectFolder={}
        state.showUpload = false;
      }
    },
  },
  actions: {},
};
