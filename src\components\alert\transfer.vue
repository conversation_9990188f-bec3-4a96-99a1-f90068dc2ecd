<template>
  <a-modal style="left: 109px;position:absolute" :maskClosable="false" width="320px" :footer="null" v-model:visible="params.visible" :title="params.title" @cancel="close">
    <div class="content">
      <div class="title">
        <div>请选择想要移交的人员</div>
        <div>
          <span><i class="icon"></i>移交后您将失去书柜主人权限，请慎重选择!</span>
        </div>
      </div>
      <div class="ztree_main ztree" id="transfer_ztree"></div>
    </div>
    <div class="footer">
      <div class="btn" @click="save">确定</div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import {
  defineProps,
  defineEmits,
  ref,
  onMounted,
  reactive,
  inject,
  h,
} from "vue";
import { Modal } from "ant-design-vue";
import { ElMessage as message } from "element-plus";
import { useStore } from "vuex";
import recycleApi from "@/api/recycle";
const store = useStore();
const { params } = defineProps({
  params: {
    type: Object,
  },
});
const getUserAuth = inject("getUserAuth");
const selectParams = reactive({
  userId: "",
  userName: "",
});
// ztree 实例
let zTree = null;
let ZTreeSetting = {
  async: {
    enable: true,
    autoParam: ["id"],
    type: "get",
    otherParam: ["bookcaseId", store.state.bookCase.uuid],
    url:
      process.env.NODE_ENV === "development"
        ? "/QSecurity/getUserAndDeptForTrans"
        : "/jchc-edisk/QSecurity/getUserAndDeptForTrans",
    dataFilter: function (treeId, parentNode, res) {
      let childNodes = res.data;
      return childNodes;
    },
  },
  check: {
    enable: true,
    chkboxType: { Y: "", N: "" },
    chkStyle: "radio",
  },
  data: {
    key: {
      title: "name",
    },
    simpleData: {
      enable: true,
      idKey: "id",
      pIdKey: "Pid",
      rootPId: "-1",
    },
  },
  callback: {
    onCheck: function (event, zTreeId, node) {
      if (node.checked) {
        selectParams.userName = node.name;
        selectParams.userId = node.id;
      }
    },
  },
};
const initZtree = () => {
  console.log(ZTreeSetting);
  zTree = $.fn.zTree.init($("#transfer_ztree"), ZTreeSetting);
};
onMounted(() => {
  initZtree();
});
const emit = defineEmits(["close"]);
const close = () => {
  emit("close");
};
const save = () => {
  if (!selectParams.userId) return message.warning("请选择想要移交的人员!");
  let user = h(
    "span",
    {
      style: {
        color: "#3162a7",
      },
    },
    selectParams.userName
  );
  let main = h("span", ["是否将书柜权限移交给", user]);
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 350,
    maskClosable: true,
    class: "alert",
    okText: () => "确认",
    cancelText: () => "取消",
    title: () => "移交确认",
    content: () => main,
    onOk: () => {
      recycleApi
        .bookcaseUpdate({
          userId: selectParams.userId,
          userName: selectParams.userName,
          uuid: store.state.bookCase.uuid,
        })
        .then((res) => {
          if (res.code == 200) {
            store.commit("clearBooCase");
            getUserAuth();
            close();
          } else {
            message.warning(res.msg);
          }
        });
    },
  });
};
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.content {
  padding: 0 23px;
  padding-top: 20px;
  font-size: $fontSize;
  .title {
    // display: flex;
    color: #3162a7;
    font-size: 14px !important;
    span {
      color: #666;
      font-size: 12px;
      display: flex;
      align-items: center;
      line-height: 24px;
      .icon {
        display: inline-block;
        margin-right: 2px;
        width: 12px;
        height: 12px;
        background: url("../../assets/img/icon/alert.png") no-repeat center
          center;
        background-size: 100% 100%;
      }
    }
  }
  .ztree_main {
    margin: 10px 0;
    height: 220px;
    width: 100%;
    overflow: auto;
    box-sizing: border-box;
    padding: 9px;
    padding-top: 5px;
    background-color: #fff;
  }
}
.footer {
  overflow: hidden;
  padding-bottom: 12px;
  padding-right: 22px;
  .btn {
    float: right;
    background-color: #3162a7;
    color: #fff !important;
    width: 60px;
  }
}
</style>