<template>
  <a-modal width="425px" :footer="null" v-model:visible="params.visible" title="移动到" @cancel="close(0)" :maskClosable="false">
    <div class="content newLabel">
      <div class="tree">
        <a-tree :fieldNames="fieldNames" v-model:expandedKeys="expandedKeys" v-model:selectedKeys="selectedKeys" :load-data="onLoadData" :tree-data="treeData" :show-line="true" :show-icon="true">
          <template #icon>
            <i class="floder" />
          </template>
        </a-tree>
      </div>
      <div class="footer alert">
        <div class="ant-btn" @click="close(0)">取消</div>
        <a-button type="primary" @click="handleOk">移动到此处</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script  lang="ts" setup>
import {
  defineProps,
  ref,
  defineEmits,
  computed,
  onMounted,
  inject,
} from "vue";
import { ElMessage as message } from "element-plus";
import { DownOutlined } from "@ant-design/icons-vue";
import { useStore } from "vuex";

import indexApi from "@/api/Index";
const store = useStore();
const fieldNames = {
  children: "children",
  title: "fileName",
  key: "uuid",
};
const { params } = defineProps({
  params: {
    type: Object,
  },
});
const expandedKeys = ref([]);
const selectedKeys = ref([]);
const emit = defineEmits(["close"]);
const close = (val) => {
  emit("close", val);
};
const handleOk = () => {
  if (!selectedKeys.value.length)
    return message.warning("请选择移动到的文件夹!");
  indexApi
    .updateBatch({
      uuids: params.filterCheck.map((item) => {
        return item.uuid;
      }),
      type: "move",
      folderId: selectedKeys.value[0],
    })
    .then((res) => {
      if (res.code == 200) {
        close(1);
      } else {
        message.warning(res.msg);
      }
    });
  // Modal.confirm({
  //   closable: true,
  //   keyboard: false,
  //   width: 423,
  //   class: "alert",
  //   okText: () => "确定",
  //   cancelText: () => "取消",
  //   title: () => "确定移动",
  //   content: () =>
  //     "是否移动?",
  //   onOk() {

  //   },
  //   onCancel() {},
  // });
};
const treeData = ref([]);
const findFolders = async (id = "") => {
  const { data, code, msg } = await indexApi.findFolders({
    uuids: params.filterCheck.map((item) => {
      return item.uuid;
    }),
    bookcaseId: store.state.bookCase.uuid,
    id,
  });
  if (code == 200) {
    if (data) {
      data.forEach((item) => {
        item.disabled = true;
        item.fileName = item.shelfName;
        item.children = [];
      });
      console.log(data);
      treeData.value = data;
    }
  } else {
    message.warning(msg);
  }
};
onMounted(() => {
  if (params.filterCheck) {
    findFolders();
  }
});
const onLoadData = async (treeNode: any) => {
  const selectParams = {
    uuids: params.filterCheck.map((item) => {
      return item.uuid;
    }),
    bookcaseId: store.state.bookCase.uuid,
  };
  let reg = /^0-[0-99999]+$/;
  if (reg.test(treeNode.pos)) {
    selectParams.shelfId = treeNode.uuid;
  } else {
    selectParams.id = treeNode.uuid;
  }
  const { data, code, msg } = await indexApi.findFolders(selectParams);
  if (code == 200) {
    if (data) {
      data.forEach((item) => {
        item.children = [];
        !item.hasChildren ? (item.class = "hide_plus") : "";
      });
    }
    treeNode.dataRef.children = [...data];
    treeData.value = [...treeData.value];
  } else {
    message.warning(msg);
  }
};
</script>
<style lang="scss" scoped>
.tree {
  height: 263px;
  width: 100%;
  overflow: auto;
  background-color: #fff;
}
.footer {
  margin-top: 25px !important;
  justify-content: flex-end !important;
}
.newLabel {
  height: auto !important;
  padding-bottom: 27px;
}
.floder {
  display: inline-block;
  width: 19px;
  height: 15px;
  background: url("../../assets/img/icon/folder_table_icon.png") no-repeat
    center center;
  background-size: 100% 100%;
  //   vertical-align: middle;
}
</style>

<style lang="scss">
.content {
  .ant-tree .ant-tree-node-content-wrapper {
    display: flex;
    align-items: center;
    line-height: normal;
    min-height: auto;
  }
  .ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle {
    display: flex;
    align-items: center;
    line-height: normal !important;
    margin-right: 10px;
  }
  .ant-tree-show-line .ant-tree-indent-unit::before {
    border: none !important;
  }
  .anticon-file {
    display: none !important;
  }

  // .ant-tree .ant-tree-treenode {
  //   margin-bottom: 10px;
  // }
  .ant-tree-title {
    font-weight: bold;
    vertical-align: sub;
  }
}
</style>