import { createRouter, createWebHistory, RouteRecordRaw,createWebHashHistory} from 'vue-router';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/index',
  },
  {
    path: '/index',
    name: 'Index',
    component: () => import(/* webpackChunkName: "index" */ '../views/Index.vue'),
  },
  {
    path: '/log',
    name: 'Log',
    component: () => import(/* webpackChunkName: "Log" */ '../views/Log.vue'),
  },
  {
    path: '/recycle',
    name: 'Recycle',
    component: () => import(/* webpackChunkName: "Log" */ '../views/Recycle.vue'),
  },
];

const router = createRouter({
  history: createWebHashHistory(process.env.BASE_URL),
  routes,
});

export default router;
