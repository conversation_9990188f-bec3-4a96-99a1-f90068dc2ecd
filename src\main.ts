import { createApp } from 'vue';
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/antd.css';
import '@/assets/zTreeStyle/zTreeStyle.css'
import App from './App.vue';
import router from './router';
import store from './store';
import '@/assets/css/reset.css';
import '@/assets/scss/common.scss';
import '@/assets/scss/dialog.scss';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import drag from '@/utils/drag'
const app = createApp(App);
app.use(Antd);
app.directive('drag', drag)
app.use(ElementPlus, { locale: zhCn });
app.use(store).use(router).mount('#app');
