import { createStore } from 'vuex';
import { useRoute,useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import requesApi from '@/api/Index';
import { h, nextTick } from 'vue';
import axios from 'axios';
import dayjs from 'dayjs';
import file from './file';
import router from '@/router/index'
let source: Function = () => {};

const myMessageError = (content: any, duration: any, onClose: any) => {
  let _remove: any;
  // 创建 VNode
  let innerText = h('span', { style: { color: '#FF1400' } }, content);
  let innerIcon = h('i', {
    style: { marginRight: '0px', marginLeft: '20px', color: '#ccc', cursor: 'pointer' },
    class: 'myErrorMsg',
    onClick: () => {
      _remove();
    },
  });
  let container = h('span', {}, [innerText, innerIcon]);

  // 调用 Message 组件
  _remove = message.error({
    content: container,
    duration: duration || 0,
    onClose: onClose,
    class: 'my_message',
  });
};

const confirm = (...val: any) => {
  let icon = h(
    'i',
    {
      style: {
        display: 'inline-block',
        borderRadius: '50%',
        width: '20px',
        heigh: '20px',
        lineHeight: '20px',
        textAlign: 'center',
        backgroundColor: '#fdb619',
        color: '#fff',
        fontSize: '16px !important',
        marginRight: '10px',
        flex: '0 0 20px',
      },
    },
    '!'
  );
  let info = h(
    'span',
    {
      class: 'info',
    },
    `您单位的服务云账户余额不足，请联系管理员${val[2].accountUserName}(${val[2].accountUserCode})续费!`
    // val[0] ?  : `当前书柜账户服务已到期，请联系书柜主人${val[1].userName}${val[1].phone ? '（' + val[1].phone + '）' : ''} !`
  );
  let phone = h(
    'p',
    {
      style: {
        fontWeight: 'bold',
        whiteSpace: 'pre-line',
      },
    },
    '盛奔宇：***********\n靳路路：***********'
  );
  let content = h(
    'div',
    {
      style: {
        padding: '30px 0',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'baseline',
      },
    },
    [icon, info]
  );
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 470,
    class: 'alert confirm',
    okText: () => '确定',
    title: () => '提示',
    cancelText: () => '取消',
    content,
    onOk() {},
  });
};

export default createStore({
  state: {
    // 用户信息
    userInfo: {
      id: '',
    },
    // 顶部书架信息
    bookCase: {
      // 书柜名称
      bookcaseName: '',
      // 已用容量
      totalSpaceAll: 0,
      // 总容量 G为单位
      allTotal: 1,
      // 书柜主人
      userName: '',
      phone: '',
      // 书柜id
      uuid: '',
      // 书柜系数
      dataSpaceNum: 0,
      count: {
        // bookshelfCounts: {
        //   num: 0,
        //   icon: require('@/assets/img/icon/floor_icon.png'),
        //   unit: '层',
        // },
        bookCase: {
          num: 0,
          icon: require('@/assets/img/icon/bookcase_icon.png'),
          unit: '个',
          title: '书柜数量',
        },
        folderCounts: {
          num: 0,
          icon: require('@/assets/img/icon/folder_icon_2.png'),
          unit: '个',
          title: '文件夹数量',
        },
        fileCounts: {
          num: 0,
          icon: require('@/assets/img/icon/file_icon_2.png'),
          unit: '个',
          title: '文件数量',
        },
      },
      linkedUser: [],
      // 书柜索引
      index: 0,
    },

    // 超管人员列表
    userAuth: [],
    // 是否超级管理员
    isAdmin: false,
    // 主页选中上传文件夹
    selectFolder: {
      isAdmin:false
    },
    // 选中文件夹的书格
    selectFile: {
      uuid:''
    },
    // 右侧复选框选中文件夹
    rightSelectFolder: {},
    // 异步加载文件夹
    asyncFolder: {},
    // 表格按钮权限
    tableLimits: {
      add: true,
      upload: true,
      limit: true,
    },
    // 到期禁用
    disabled: false,
    fileNum: 0,
  },
  mutations: {
    setFileNum(state, val) {
      state.fileNum += 1;
    },
    setUserInfo(state, val) {
      state.userInfo = val;
    },
    setUserAuth(state, val) {
      state.userAuth = val;
    },
    setAdmin(state, val) {
      state.isAdmin = val;
    },
    setBookCase(state, val) {
      if (val) {
        let oldUuid = state.bookCase.uuid;
        state.bookCase.userName = val.userName;
        state.bookCase.phone = val.phone;
        state.bookCase.uuid = val.uuid;
        // state.bookCase.count.bookshelfCounts.num = val.bookshelfCounts | 0;
        // state.bookCase.count.fileCounts.num = val.fileCounts | 0;
        // state.bookCase.count.folderCounts.num = val.folderCounts | 0;
        state.bookCase.totalSpaceAll = val.totalSpaceAll;
        // 将系数*1024 1 = 1T 换算成G
        state.bookCase.allTotal = val.dataSpaceNum ? val.dataSpaceNum * 1024 : 1;
        state.bookCase.linkedUser = val.linkedUser;
        state.bookCase.index = val.sort - 1;
        state.bookCase.bookcaseName = val.bookcaseName;
        state.bookCase.dataSpaceNum = val.dataSpaceNum;

        val.userId === state.userInfo.id ? (state.isAdmin = true) : (state.isAdmin = false);
        let path =(router.currentRoute as any).value.path
        if (oldUuid !== val.uuid && path=='/index') {
          if (typeof source === 'function') {
            source();
          }
          state.disabled = false;
          requesApi
            .queryBookcaseTime(
              { bookcaseId: val.uuid },
              new axios.CancelToken(function executor(c) {
                source = c;
              })
            )
            .then((res) => {
              if (res.data !== 500) {
                /**
                 * -1 不限天数
                 * 0 到期
                 * 大于0可用天数
                 */
                requesApi
                  .findAccountAdminByOrgs({
                    bookcaseId: val.uuid,
                  })
                  .then((adminValue: any) => {
                    let admin = {
                      accountUserName: '',
                      accountUserCode: '',
                    };
                    admin = adminValue.data[0];
                    if (!res.data.data) {
                      state.disabled = true;
                      confirm(state.isAdmin, state.bookCase, admin);
                    } else if (res.data.data != -1 && res.data.data <= 30) {
                      if (state.isAdmin) {
                        myMessageError(`您的账户将于${res.data.data}天后（${dayjs().add(res.data.data, 'days').format('YYYY-MM-DD')}）到期，为了不影响您的正常使用，请联系服务云管理员${admin.accountUserName}（${admin.accountUserCode}）续费`, 5, () => {});
                        nextTick(() => {
                          $('.my_message').parents('.ant-message').addClass('my_ant_msg');
                        });
                      }
                    }
                  });
              }
            });
        }
      }
    },
    setSelectFolder(state, val) {
      state.selectFolder = val;
      state.selectFolder.isAdmin = state.isAdmin
    },
    // 选中书的书格
    setSelectFile(state, val) {
      state.selectFile = val;
    },
    setRightSelectFolder(state, val) {
      state.rightSelectFolder = val;
    },
    setAsyncFolder(state, val) {
      state.asyncFolder = val;
    },
    // 清空
    setClear(state) {
      state.asyncFolder = {};
      // state.rightSelectFolder = {};
    },
    setBookCaseNum(state, val) {
      state.bookCase.count.bookCase.num = val;
    },
    clearBooCase(state, val) {
      state.bookCase.bookcaseName = '';
      state.bookCase.uuid = '';
      state.bookCase.totalSpaceAll = 0;
      state.bookCase.allTotal = 2;
      state.bookCase.linkedUser = [];
      state.bookCase.count.fileCounts.num = 0;
      state.bookCase.count.folderCounts.num = 0;
    },
    setCount(state, val) {
      state.bookCase.count.fileCounts.num = val.fileCounts | 0;
      state.bookCase.count.folderCounts.num = val.folderCounts | 0;
    },
  },
  actions: {},
  modules: {
    file,
  },
});
