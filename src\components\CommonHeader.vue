<template>
  <div class="common_header" :class="{ recycle: type === 2 }">
    <div class="owner" v-if="bookCase.userName" :style="{backgroundImage:'url('+(path=='/index' ? require('@/assets/img/icon/master_2.png'): require('@/assets/img/icon/master_gray_2.png'))+')'}">
      <div class="owner_top">
        <div class="userName" @click="transfer" title="书柜主人" :style="{color:path=='/index'?'':'#fff',cursor :isAdmin && path=='/index'?'pointer':'default'}">{{ bookCase.userName }}</div>
        <template v-for="(item, index) in settingIcon" :key="index">
          <i style="width:12px;height:14px" v-if="index == 'authority' && isAdmin && path=='/index'" @click="handleCurrent(item)" :title="bookCase.linkedUser.map(item=>{return item.name}).join('、')" :style="{ backgroundImage: 'url(' + item.icon + ')' }"></i>
        </template>
      </div>
      <div class="owner_bottom">
        <div :title="bookCase.bookcaseName" :class="path=='/index' ?'folder_name':'recycle_folder_name'" :style="{cursor :isAdmin && path=='/index'?'pointer':'default'}" v-if="!editBookcaseNameShow" @dblclick="editBookcaseName">{{bookCase.bookcaseName}}</div>
        <a-input id="refbookName" style="height:19px;" v-model:value="bookcaseName" v-else @keyup.enter.native="$event.target.blur()" @blur="upDateShelfName" />
      </div>

    </div>

    <div class="btn_bg" v-if="type == 2 && bookCase.userName && isAdmin" @click="clearFile">
      <img src="../assets/img/icon/right-clear.png" alt="" />
      清空回收站
    </div>
    <div class="left_title">
      <slot name="left_title"></slot>
    </div>
    <div class="search">
      <el-input style="width: 225px;margin-right:15px" placeholder="请输入关键字搜索" v-model="keyword" size="small" @keypress.enter="searchBook">
        <template #suffix>
          <div class="icon_content">
            <i class="search_icon" @click="searchBook"></i>
          </div>
        </template>
        <!-- <template #prepend>
          <el-select
            v-model="searchType"
            placeholder=""
            size="small"
            style="width: 90px"
          >
            <el-option label="文件" value="1"></el-option>
            <el-option label="关键词" value="2"></el-option>
          </el-select>
        </template> -->
      </el-input>
    </div>
    <div class="right">
      <template v-if="type == 1">
        <!-- <div class="label">标签筛选：</div> -->
        <a-rate v-model:value="level" :count="3" @change="searchBook" />
        <!-- <el-rate v-model="level" :max="3" @change="searchBook"></el-rate> -->
        <div class="setting">
          <template v-for="(item, index) in settingIcon" :key="index">
            <i v-if="index == 'recycle'" @click="handleCurrent(item)" :title="item.title" :style="{ backgroundImage: 'url(' + item.icon + ')' }"></i>
          </template>
        </div>
      </template>
      <template v-else>
        <i class="back" @click="back"></i>
      </template>
    </div>
  </div>
  <!-- 权限配置  -->
  <bookshelf-profile v-if="bookshelfProfileVisible" :visible="bookshelfProfileVisible" @close="bookshelfProfileClose"></bookshelf-profile>
  <!-- 转交书柜主人  -->
  <transfer-dialog  v-if="transferParams.visible" :params="transferParams" @close="transferClose"></transfer-dialog>
</template>
<script lang="ts" setup>
import {
  reactive,
  ref,
  defineProps,
  computed,
  onMounted,
  onUnmounted,
  inject,
  defineEmits,
  nextTick,
  watch,
  h
} from "vue";
import { Modal } from "ant-design-vue";
import { ElMessage as message } from "element-plus";

import indexApi from "@/api/recycle";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import BookshelfProfile from "./rightsProfile/BookshelfProfile.vue";
import recycle from "@/api/recycle";
import transferDialog from "./alert/transfer.vue";
const store = useStore();
const router = useRouter();
const route = useRoute();
const updateName = inject("updateName");
const getBookCase = inject("getBookCase");
const bookCase = computed(() => store.state.bookCase);
const isAdmin = computed(() => store.state.isAdmin);
const emit = defineEmits(["searchBook"]);
let path = computed(() => {
  return route.path;
});
let bookshelfProfileVisible = ref(false);
const { type } = defineProps({
  /**
   * 1 文件夹页面
   * 2 回收站页面
   */
  type: {
    type: Number,
  },
});
let keyword = ref();
let level = ref(0);
let searchType = ref("1");
/**
 * log recycle 跳转页面
 */
// 权限按钮
const settingIcon = reactive({
  // log: {
  //   icon: require('@/assets/img/icon/log_icon.png'),
  //   title: '日志',
  //   path: '/log'
  // },
  recycle: {
    icon: require("@/assets/img/icon/recycle_icon.png"),
    title: "回收站",
    path: "/recycle",
  },
  authority: {
    icon: require("@/assets/img/icon/authority_table_icon.png"),
    title: "书柜权限配置",
  },
});
onMounted(() => {});
onUnmounted(() => {
  $(document).unbind();
});

// 跳转页面/分配权限
const handleCurrent = (val) => {
  if (val.path) {
    router.push(val.path);
  } else {
    // 配置权限
    bookshelfProfileVisible.value = true;
  }
};
// 清空回收站
const clearFile = () => {
  console.log(store.state.bookCase.uuid);
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "确定删除",
    content: () => "确定清空回收站吗?",
    onOk() {
      indexApi
        .file_clear({
          bookcaseId: store.state.bookCase.uuid,
        })
        .then((res) => {
          if (res.code == 200) {
            // emit("searchBook", {
            //   searchType: searchType.value,
            //   keyword: keyword.value,
            //   level: level.value,
            // });
            store.commit("setSelectFolder", {});
            getBookCase();
          } else {
            message.warning(res.msg);
          }
        });
    },
    onCancel() {},
  });
};
// 关闭权限配置页面
const bookshelfProfileClose = () => {
  bookshelfProfileVisible.value = false;
};
const back = () => {
  router.push("/index");
};
const searchBook = () => {
  emit("searchBook", {
    searchType: searchType.value,
    keyword: keyword.value,
    level: level.value,
  });
};
let bookcaseName = ref("");
let editBookcaseNameShow = ref(false);
const editBookcaseName = () => {
  if (!isAdmin.value || path.value !== "/index") return;
  bookcaseName.value = store.state.bookCase.bookcaseName;
  editBookcaseNameShow.value = true;
  // 自动获取焦点
  nextTick(() => {
    $("#refbookName").focus();
  });
};
const upDateShelfName = async () => {
  if (!isAdmin.value) return;
  console.log(bookcaseName.value + "");
  if (!bookcaseName.value) return message.warning("请输入书柜名称");
  const { data, code, msg } = await recycle.bookcaseUpdate({
    bookcaseName: bookcaseName.value,
    uuid: store.state.bookCase.uuid,
  });
  if (code == 200) {
    updateName(bookcaseName.value);
    bookcaseName.value = "";
    editBookcaseNameShow.value = false;
  } else {
    message.warning(msg);
  }
};

// 书柜主人转交弹框
const transferParams = reactive({
  visible: false,
  title: "书柜主人移交",
});
// 书柜主人转交
const transfer = () => {
  if (isAdmin.value && path.value == "/index") {
    transferParams.visible = true;
  }
};

const transferClose = ()=>{
  transferParams.visible =false
}
watch(
  store.state.bookCase,
  (newVal) => {
    editBookcaseNameShow.value = false;
    bookcaseName.value = "";
  },
  { deep: true }
);
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.recycle.common_header {
  position: relative;
  background: url("../assets/img/background/recycle_bg.png") no-repeat center
    center;
}
.common_header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background: url("../assets/img/background/header_bg.png") no-repeat center
    center;
  background-size: 100% 100%;
  padding: 0 24px;
  padding-left: 10px;
  > div {
    // flex: 1;
  }
  div.btn_bg {
    img {
      margin-right: 3px;
    }
    position: absolute;
    right: 8%;
    padding: 0 10px;
  }
  .left_title {
    display: flex;
    align-items: center;
    height: 100%;
  }
  .search {
    display: flex;
    align-items: center;
    justify-content: center;

    .el-input__suffix {
      display: flex;
      align-items: center;
    }
    .icon_content {
      display: flex;
      align-items: center;
      .search_icon {
        width: 14px;
        height: 14px;
        background: url("../assets/img/icon/search_icon.png") no-repeat center
          center;
        background-size: 100% 100%;
        cursor: pointer;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;

    .label {
      color: #fff;
      font-size: $fontSize;
    }
    .setting {
      display: flex;
      align-items: center;
      i {
        display: inline-block;
        width: 21px;
        height: 21px;
        background-size: 100% 100%;
        margin-left: 22px;
        cursor: pointer;
        &:nth-child(2),
        &:nth-child(3) {
          width: 19px;
        }
      }
    }
    .back {
      display: inline-block;
      width: 24px;
      height: 22px;
      background: url("../assets/img/icon/back_icon.png") no-repeat center
        center;
      background-size: 100% 100%;
      cursor: pointer;
    }
  }
}
.owner {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: absolute;
  text-align: left;
  padding: 0 6px;
  left: 0;
  top: 64px;
  z-index: 999;
  width: 97px;
  height: 73px;
  background: url("..//assets/img/icon/master_2.png") no-repeat center center;
  background-size: 100% 100%;
  // writing-mode: tb-rl;
  span {
    display: block;
    margin-top: 20px;
    color: #fff;
    font-size: $fontSize;
    margin-right: 32px;
    letter-spacing: 5px;
  }
  .userName {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    position: relative;
    font-size: $fontSize;
    color: #62c2ff;
    padding-right: 10px;
  }
  i {
    // margin-left: 10px;
    // left: 52%;
    display: inline-block;
    width: 19px;
    height: 21px;
    background-size: 100% 100%;
    cursor: pointer;
  }
}
.owner_top {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.owner_bottom {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 25px;
  width: 100%;
  .folder_name {
    height: 19px;
    line-height: 19px;
    display: inline-block;
    font-size: 12px;
    color: #fff;
    text-align: center;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    background-color: #1488c8;
    border-radius: 4px;
  }
  .recycle_folder_name {
    height: 19px;
    line-height: 19px;
    display: inline-block;
    font-size: 12px;
    color: #fff;
    text-align: center;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    background-color: #6e7279;
    border-radius: 4px;
  }
}
</style>
<style>
.el-input-group__append,
.el-input-group__prepend {
  background-color: #fff;
  border: none;
}
</style>
