<template>
  <!-- Slider main container -->
  <div class="swiper book_shelf">
    <div class="swiper-wrapper">
      <!-- 书格  超过五层上下轮播 -->
      <div class="swiper-slide" v-for="(items, indexs) in props.data" :key="items.uuid">
        <div class="layer">
          <!-- <span class="floor_num "><span>{{index+1}}</span>/5</span> -->
          <span :title="items.shelfName" class="folder_name"
            :style="{ cursor: (isAdmin || userInfo.id == items.userId) ? 'pointer' : 'default' }" v-if="!items.editName"
            @dblclick="editShelfName(items)">{{ items.shelfName }}</span>
          <a-input id="refShelfName" class="initShelfName" v-model:value="folderName" v-if="items.editName"
              @keyup.enter.native="upDateShelfName(items, 1)" >
  <template #suffix>
       <div> <i class="checkOk" v-if="items.editName" title="保存" @click="upDateShelfName(items, 1)"></i> </div>
        </template>
              </a-input>
               <!-- @keyup.enter.native="$event.target.blur()"  @blur="upDateShelfName(items, 1)" -->
         
            <!-- @blur="upDateShelfName(items, 1)" -->
         
          <!-- <a-input class="initShelfName" v-model:value="items.folderName" v-if="isAdmin && !items.shelfName" @keyup.enter.native="$event.target.blur()" @blur="upDateShelfName(items,2)" /> -->
          <div class="userRight">
            <span class="user" v-if="items.isLabel !== 1" title="书格主人">
              <span :style="{ cursor: store.state.isAdmin ? 'pointer' : 'default' }"
                v-if="items.userName && items.userName !== '未分配'" @click="settingOwner(items, indexs)">{{ items.userName
                }}</span>
              <span :style="{ cursor: store.state.isAdmin ? 'pointer' : 'default' }" v-else
                style="color:rgba(19,166,243,0.4)" @click="settingOwner(items, indexs)">未分配</span>
              <ul v-if="items.showOwnerSet" :id="'ztree_' + indexs" class="ztree">

              </ul>
            </span>
            <i class="authority" v-if="isAdmin || userInfo.id == items.userId"
              :title="items.linkedUser && items.linkedUser.map(item => { return item.name }).join('、')"
              @click="handleCheck(1, items)"></i>
               <i class="delete_icon" title="删除书格" style="height:16px;width:16px" @click="deleteBookShelf(items, indexs)"
              v-show="items.editName && indexs >= 5 && isAdmin"></i>
          </div>
        </div>
        <div class="book_item">
          <!-- :style="{zIndex:(items.fileFolderVos.length-index) * (data.length - indexs+1)}" -->
          <!-- 文件夹  -->
          <div class="folder_item" id="folder_item" v-for="(item, index) in items.fileFolderVos" :key="item.uuid">
            <div :data-string="JSON.stringify(item)" :style="{ backgroundColor: item.colorStr }" class="folder" @click="handleSelectFolder(item, items)"
              :class="selectFolder.uuid == item.uuid ? 'active' : ''">
              <div class="folder_name" @click.stop="editFileName(item, items)">
                <div class="scroll_name" @mouseleave="floderNameMouseOut" @mouseenter.self="floderNameMouseEnter">
                  <!-- <div>{{item.fileName}}</div> -->
                  <div @mouseleave="floderNameMouseOut" @mouseenter.self="floderNameMouseEnter">
                    <span v-for="(itemx, indexx) in item.fileName" :key="indexx">{{ itemx }}</span>
                  </div>
                </div>
              </div>
              <!-- <a-popover v-if="isAdmin || userInfo.id==items.userId" color="#0375de" v-model:visible="item.visible" trigger="click" placement="top">
                <template #content>
                  <div class="hover_book">
                    <i class="del" title="删除" @click="deleteBook(items,item,index)"></i>
                    <i class="rol" title="书权限配置" @click="handleCheck(2,items,item)"></i>
                    <i class="remove" @click="closeBook(item)" title="关闭"></i>
                  </div>
                </template>
                <div class="folder_num" @click.stop="(item)=>{reset();item.visible=true}">{{item.fileFolderCounts}}</div>
              </a-popover> -->
              <div class="folder_num">
                <span :class="(item.fileFolderCounts).toString().length >= 3 ? 'tranformStyle' : ''"
                  style="display: inline-block;">{{ item.fileFolderCounts <= 999 ? item.fileFolderCounts : '999+'
                  }}</span>
              </div>
            </div>
            <!-- 文件名弹框 -->
            <div :key="new Date().getTime()" class="newInput" v-if="item.edit" @click.stop=""
              :class="{ 'newInputWz': indexs == 4, 'leftNewInputWz': index >= 22 }">
              <div class="newInput_top">
                <a-input :maxlength="50" v-model:value.trim="folderName" @keyup.enter="editSaveFileName(0, item)">
                </a-input>
                <i class="delete_icon" title="删除书" @click="deleteBook(items, item, index)"></i>
              </div>
              <div class="edit_ctrl">
                <vColorPickers :value="item.colorStr" @change="(val) => { editSaveFileName(1, item, val) }">
                </vColorPickers>
                <!-- <div class="delete_book">
                  <h3>删除书</h3> -->
                <!-- </div> -->
              </div>

            </div>
          </div>
          <!-- 新建文件夹  -->
          <div class="add_floder"
            v-if="items.fileFolderVos?.length < 25 && (isAdmin || userInfo.id == items.userId) && (!searchParams.keyword && !searchParams.fileName && !searchParams.level)">
            <div class="folder">
              <div @click="openAdd(items)" class="folder_name"></div>
              <div class="folder_num"></div>
            </div>
            <div class="newInput" v-if="items.add"
              :class="{ 'newInputWz': indexs == 4, 'leftNewInputWz': items.fileFolderVos.length >= 21 }">
              <a-input :maxlength="50" v-model:value.trim="folderName" @keyup.enter="hideAdd(items)" />
              <vColorPickers :value="defaultColor" @change="(val) => { changeAddBg(val, items) }"></vColorPickers>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <SettingUser :type="type" v-if="settingUserParams.visible" :params="settingUserParams" @close="closeSettingUser">
  </SettingUser>
</template>
<script setup lang="ts">
import {
  nextTick,
  ref,
  defineProps,
  reactive,
  watch,
  computed,
  inject,
  onMounted,
  onUnmounted,
  defineExpose
} from "vue";
import vColorPickers from "@/components/vColorPickers.vue";
import SettingUser from "./rightsProfile/SettingUser.vue";
import settingApi from "@/api/setting";
import indexApi from "@/api/Index";
import { Modal } from "ant-design-vue";
import { ElMessage as message } from "element-plus";
import { CheckOutlined } from '@ant-design/icons-vue';
import { useStore } from "vuex";
import utils from "@/utils/utils";
const store = useStore();
let zTree_item: any = null;
const deleteBookShelfProvide = inject('deleteBookShelf')
const getUserAuth = inject("getUserAuth");
const props= defineProps({
  data: {
    type: Array,
    default:()=>[]
  },
});
const isAdmin = computed(() => store.state.isAdmin);
const userInfo = computed(() => store.state.userInfo);
/**
 * 2书格
 * 3书
 */
let type = ref(2);
// 新建文件夹名称
let folderName = ref();
const dataList = ref([]);
const searchParams = inject("searchParams");
// 双击事件定时器
let clickTime: any = null;

// 删除书格
const deleteBookShelf= (item,index)=>{
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "确定删除",
    content: () => "是否确认删除书格？删除后所有书格中的文件或文件夹清空，且数据不可恢复？",
   async onOk() {
    const {data,code,msg} = await indexApi.bookshelfDelete({
      uuid:item.uuid
    })
    if(code==200){
        store.commit("setSelectFolder", {});
        deleteBookShelfProvide(index)
    }else{
        message.warning(msg);
    }
    },
    onCancel() { },
  });

}

// 重置状态
const reset = () => {
  defaultColor.value = "";
  zTree_item ? zTree_item.destroy() : "";
  props.data.forEach((item) => {
    item.add = false;
    item.editName = false;
    item.showOwnerSet = false;
    item.fileFolderVos?.forEach((items) => {
      items.edit = false;
      items.visible = false;
    });
  });
  folderName.value = "";
};

onMounted(() => {
  document.addEventListener("click", (e) => {
    if (
      $(e.target).closest(".book_item").length &&
      !$(e.target).closest(".folder,.folder_items").length
    ) {
      store.commit("setClear");
      store.commit("setSelectFolder", {});
    }
    if ($(e.target).attr("class") == "book_item") {
      reset();
    }
  });
});
onUnmounted(() => {
  document.removeEventListener("click", () => { });
  $(document).unbind();
});
const selectFolder = computed(() => store.state.selectFolder);
for (let i = 0; i < 10; i++) {
  dataList.value.push({
    visible: false,
  });
}
let swiper: any = ref();
defineExpose({
  swiper,
  type
})
nextTick(() => {
  swiper.value = new Swiper(".book_shelf", {
    direction: "vertical",
    slidesPerView: 5,
    nested: true,
    loop: false,
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
  });
});
// 选中书架文件夹
const handleSelectFolder = (val: object, items: object) => {
  val.isAdmin = store.state.isAdmin;
  store.commit("setSelectFolder", val);
  store.commit("setSelectFile", items);
  store.commit("setRightSelectFolder", {});
  store.commit("setAsyncFolder", {});
};
// 文字滚动定时器
let timer: any = null;
// 鼠标移入文件夹名称滚动动画
const floderNameMouseEnter = (e: any) => {
  if (timer) {
    clearInterval(timer);
  }
  let scrollHeight = $(e.target)[0].scrollHeight;
  let offsetHeight = $(e.target)[0].offsetHeight;
  if (scrollHeight > offsetHeight) {
    timer = setInterval(() => {
      $(e.target).animate(
        {
          marginTop: "-=1",
        },
        0,
        function () {
          var s = Math.abs(parseInt($(e.target).css("margin-top")));
          if (s >= scrollHeight - offsetHeight) {
            $(e.target).find("div").slice(0, 1).appendTo($(e.target));
            $(e.target).css("margin-top", 0);
          }
        }
      );
    }, 80);
  }
};
// 鼠标移出文件夹名称
const floderNameMouseOut = (e: any) => {
  clearInterval(timer);
  if ($(e.target).parent().hasClass("scroll_name")) {
    $(e.target).parent().css("margin-top", 0);
    $(e.target).css("margin-top", 0);
  }
};
// 打开新建书本弹框
const openAdd = (val: any) => {
  reset();
  val.add = true;
};
let model = null;
const saveFolder = (type, params, val) => {
  indexApi[!type ? "addFolderAuth" : "fileFolderAdd"](params).then((res) => {
    if (res.code == 200) {
      folderName.value = "";
      defaultColor.value = "";
      getUserAuth();
    } else {
      message.warning(res.msg);
    }
    val.add = false;
    model = null;
  });
};
// 新建书本文件夹保存
const hideAdd = async (val: any) => {
  if (!folderName.value) return message.warning("请输入书本名称!");
  let index = val.fileFolderVos.findIndex((item) => {
    return item.fileName == folderName.value;
  });
  if (index != -1) {
    return message.warning("该名称已存在，请重新命名!");
  }
  let params = {
    uuid: utils.uuid(),
    fileName: folderName.value,
    type: "folder",
    bookcaseId: store.state.bookCase.uuid,
    shelfId: val.uuid,
    level: 0,
    colorStr: defaultColor.value,
  };
  saveFolder(0, params, val);
  // if (model) return;
  // model = Modal.confirm({
  //   keyboard: false,
  //   width: 433,
  //   class: "alert saveFlod",
  //   okText: () => "确定",
  //   cancelText: () => "取消",
  //   title: () => "开放权限",
  //   content: () =>
  //     "是否开放本书的查看权限？(开放权限：拥有书格查看权限的人员，可以看到此书。不开放权限：只能自己看到。)",
  //   onOk() {
  //     saveFolder(0, params, val);
  //   },
  //   onCancel() {
  //     saveFolder(1, params, val);
  //   },
  // });
};
let defaultColor = ref("");
const changeAddBg = (val, items) => {
  defaultColor.value = val;
  hideAdd(items);
};
// 配置人员参数
let settingUserParams = reactive({
  visible: false,
  title: "",
  alert: "拥有书格查看权限的人员或部门",
  // 书格id
  uuid: "",
  // 书id
  fileUuid: "",
});
const handleCheck = (val: any, items: any, item: any) => {
  store.commit("setSelectFile", items);
  settingUserParams.uuid = items.uuid;
  if (val == 1) {
    type.value = 2;
    settingUserParams.alert = "拥有书格使用权限的人员或部门";
    settingUserParams.title = '书格权限配置'
  } else if (val == 2) {
    item.visible = false;
    settingUserParams.alert = "拥有书使用权限的人员或部门";
    settingUserParams.title = '书格权限配置'
    settingUserParams.fileUuid = item.uuid;
    type.value = 3;
  }
  settingUserParams.visible = true;
};
const closeSettingUser = () => {
  settingUserParams.visible = false;
};
// 关闭书本弹框
const closeBook = (val: any) => {
  val.visible = false;
};
const deleteBook = (vals: any, val: any, index: number) => {
  closeBook(val);
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "确定删除",
    content: () => "确定删除该文件夹吗?删除后在30天内可以通过回收站还原",
    onOk() {
      indexApi
        .fileFolderDelete({
          uuid: val.uuid,
        })
        .then((res) => {
          if (res.code == 200) {
            if (val.uuid == store.state.selectFolder.uuid) {
              store.commit("setClear");
              store.commit("setSelectFolder", {});
            }
            getUserAuth();
          } else {
            message.warning(res.msg);
          }
        });
    },
    onCancel() { },
  });
};
const editShelfName = (val) => {
  if (isAdmin.value || userInfo.value.id == val.userId) {
    props.data.forEach((item) => {
      item.editName = false;
    });
    folderName.value = val.shelfName;
    val.editName = true;
    // 自动获取焦点
    nextTick(() => {
      $("#refShelfName").focus();
    });
  }
};
// 修改书格名称
const upDateShelfName = async (val, type) => {
  console.log(val, type);
  if (type == 1) {
    if (!folderName.value) {
      return message.warning("请输入书格名称!");
    }
  } else {
    if (!val.folderName) {
      return message.warning("请输入书格名称!");
    }
  }
  let parmas = {
    bookcaseId: store.state.bookCase.uuid,
    uuid: val.uuid,
    shelfName: type == 1 ? folderName.value : val.folderName,
  };
  const { data, code, msg } = await indexApi.update(parmas);
  if (code == 200) {
    val.editName = false;
    if (type == 1) {
      val.shelfName = folderName.value;
      folderName.value = "";
    } else {
      val.shelfName = val.folderName;
      val.folderName = "";
    }
  } else {
    message.warning(msg);
  }
};
// 打卡修改书的名称弹框
const editFileName = (val, vals) => {
  clickTime = new Date().getTime();
  setTimeout(() => {
    if (new Date().getTime() - clickTime < 300) {
      if (isAdmin.value || userInfo.value.id == vals.userId) {
        reset();
        val.edit = true;
        folderName.value = val.fileName;
      }
    }
    handleSelectFolder(val, vals);
  }, 300);
};
// 修改书
const editSaveFileName = async (type, item, val) => {
  if (!folderName.value) return message.warning("请输入书本名称!");

  let index = store.state.selectFile.fileFolderVos
    .filter((items) => {
      return items.uuid != item.uuid;
    })
    .findIndex((itemIndex) => {
      return itemIndex.fileName == folderName.value;
    });
  if (index !== -1) {
    return message.warning("该名称已存在，请重新命名!");
  }

  let params = {
    uuid: item.uuid,
    fileName: folderName.value,
  };
  type ? (params.colorStr = val) : "";
  const { data, code, msg } = await indexApi.fileFolderUpdate(params);
  if (code == 200) {
    item.fileName = folderName.value;
    folderName.value = "";
    item.edit = false;
    type ? (item.colorStr = val) : "";
  } else {
    message.warning(msg);
  }
};
let selectOwner = reactive(null);
// 插入书格主人
const insertShelfAdmin = async () => {
  const { data, code, msg } = await settingApi.insertShelfAdmin({
    userId: selectOwner.userId,
    userName: selectOwner.userName,
    bookcaseId: store.state.bookCase.uuid,
    bookcaseNum: selectOwner.index + 1,
  });
  if (code != 200) {
    message.warning(msg);
  }
};
const setFontCss = (treeId, treeNode) => {
  return treeNode.type == "button" ? { color: "red" } : {};
};
let ZTreeSettingItem = {
  async: {
    enable: true,
    autoParam: ["id"],
    otherParam: ["bookcaseId", store.state.bookCase.uuid],
    type: "get",
    url:
      process.env.NODE_ENV === "development"
        ? "/QSecurity/getUserAndDeptForShelf"
        : "/jchc-edisk/QSecurity/getUserAndDeptForShelf",
    dataFilter: function (treeId, parentNode, res) {
      var childNodes = res.data;
      res.data.forEach((item) => {
        if (selectOwner.userId == item.id) {
          item.checked = true;
        }
      });
      return childNodes;
    },
  },
  check: {
    chkboxType: {
      Y: "",
      N: "",
    },
    chkStyle: "radio",
    // radioType: "all",
    enable: true,
  },
  data: {
    simpleData: {
      enable: true,
      idKey: "id",
      pIdKey: "parentId",
      rootPId: "0",
    },
  },
  view: {
    showIcon: false,
    // fontCss: setFontCss,
  },
  callback: {
    onCheck: function (event, zTreeId, node) {
      if (node.checked) {
        console.log(node);
        selectOwner.userId = node.id;
        selectOwner.userName = node.name;
        insertShelfAdmin();
      } else {
        selectOwner.userId = node.id;
        selectOwner.userName = node.name;
        insertShelfAdmin();
      }
      selectOwner.showOwnerSet = false;
    },
  },
};
// 配置书格主人
const settingOwner = (val, indexs) => {
  if (!store.state.isAdmin) return;
  reset();
  val.showOwnerSet = true;
  val.index = indexs;
  selectOwner = val;
  nextTick(() => {
    zTree_item = $.fn.zTree.init($(`#ztree_${indexs}`), ZTreeSettingItem);
  });
};
</script>
<style lang="scss" scoped>
$fontSize: 12px;

.newInput_top {
  width: 100%;
  display: flex;
  align-items: center;
}

.swiper {
  width: 100%;
  height: 100%;
  margin: 0;

  .swiper-slide {
    position: relative;
    display: flex;
    align-items: flex-end;
    background-color: rgba(0, 0, 0, 0.3);
    height: 100%;
    background: url("../assets/img/background/bookshelf.png") no-repeat center center;
    background-size: 100% 100%;
    padding-left: 4.5%;
    padding-right: 3%;

    .layer {
      width: 22%;
      height: 24px;
      display: flex;
      align-items: center;
      position: absolute;
      top: -0.5px;
      left: 1.2%;

      span {
        height: 19px;
      }

      .userRight {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        flex: 1;
      }

      .floor_num {
        font-size: $fontSize;
        color: #fff;

        >span {
          color: #13a6f3;
        }
      }

      .folder_name {
        display: inline-block;
        position: relative;
        font-size: $fontSize;
        color: #fff;
        text-align: left;
        padding-left: 10px;
        // margin-left: 8px;
        width: 130px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        line-height: 20px;
        background-color: #1488c8;
        border-radius: 4px;
      }

      .initShelfName {
        // background-color: rgba(0, 0, 0, 0.2);
        // border: 1px dashed rgba(19, 166, 243, 0.3);
        // color: #fff;
        border: none;
        background-color: #fff;
        // color: #fff;
        width: 130px;
        padding: 4px;
        height: 20px;
        margin-top: -1px;
      }

      .user {
        height: 100%;
        position: relative;
        font-size: $fontSize;
        color: #13a6f3;
        margin-right: 6px;
        cursor: pointer;

        >span {
          display: inline-block;
          line-height: 24px;
          max-width: 60px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        ul {
          position: absolute;
          top: 18px;
          width: 200px;
          height: 140px;
          background-color: #fff;
          z-index: 99;
          border-radius: 3px;
          left: 50%;
          transform: translateX(-50%);
          box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
          overflow: auto;
        }
      }

      .authority {
        display: inline-block;
        width: 12px;
        height: 14px;
        background: url("../assets/img/icon/authority2_icon.png") no-repeat center center;
        background-size: 100% 100%;
        vertical-align: middle;
        cursor: pointer;
      }
    }
  }

  .book_item {
    display: flex;
    width: 100%;
    height: 100%;
    white-space: nowrap;
    align-items: flex-end;
    // overflow-x: auto;
    padding-bottom: 1.2%;

    .active {
      position: relative;
      top: -12px;
      transform: scale(1.2);
    }

    .folder_item {
      position: relative;
      display: inline-block;
      flex: 0 0 27px;
      height: 70%;
      margin-right: 4px;

      // &:nth-child(3n + 1) {
      .folder {
        // background-color: linear-gradient(270deg, #0049e7 0%, #5086ff 100%);
        background-color: #72349d;
      }

      // }
      // &:nth-child(3n - 1) {
      //   .folder {
      //     background: linear-gradient(270deg, #0090ed 0%, #36ceff 100%);
      //   }
      // }
      // &:nth-child(3n) {
      //   .folder {
      //     background: linear-gradient(270deg, #fc9100 0%, #ffae41 100%);
      //   }
      // }
    }

    .folder {
      position: relative;
      border-radius: 3px;
      width: 100%;
      height: 100%;
      cursor: pointer;
      transition: 0.2s transform ease-in-out;
      overflow: hidden;

      &:hover {
        transform: scale(1.2);
      }

      .folder_name {
        position: relative;
        width: 74%;
        height: 65%;
        background-color: #fff;

        font-size: $fontSize;
        text-align: center;
        border-radius: 2px;
        // writing-mode: vertical-lr;
        margin: 0 auto;
        margin-top: 10px;
        overflow: hidden;

        .scroll_name {
          display: flex;
          flex-direction: row;
          height: 100%;
          width: 100%;

          div {
            display: flex;
            flex-direction: column;
            width: 100%;
            align-items: center;
            font-size: $fontSize;
            color: #666666;
            // line-height: 19px;
          }

          // -webkit-animation: 10s rowup linear infinite normal;
          // animation: 10s rowup linear infinite normal;
          // animation: move 5s infinite alternate linear;
        }
      }

      .folder_num {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        margin: 0 auto;
        border-radius: 50%;
        font-size: $fontSize;
        margin-top: 20%;
        background-color: #fff;
        color: #666666;
      }
    }

    .add_floder {
      position: relative;
      display: inline-block;
      border-radius: 3px;
      margin-right: 4px;
      flex: 0 0 27px;
      height: 70%;
      cursor: pointer;

      .folder {
        transition: 0.2s transform ease-in-out;
        background: linear-gradient(270deg, #c6d2e4 0%, #ffffff 100%);

        &:hover {
          transform: scale(1.2);
        }
      }

      .folder_name {
        margin: 0 auto;
        margin-top: 10px;
        height: 65%;
        width: 74%;
        border: 1px dashed #61636c;
        border-radius: 2px;
      }

      .folder_num {
        width: 18px;
        height: 18px;
        margin: 0 auto;
        border-radius: 50%;
        margin-top: 20%;
        background-color: #fff;
        border: 1px dashed #61636c;
      }
    }
  }
}

.newInput {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // width: 148px;
  // height: 44px;
  background-color: #fff;
  border-radius: 4px;
  position: absolute;
  right: -231px;
  top: -22px;
  padding: 6px;
  z-index: 99999;

  &::before {
    display: inline-block;
    position: absolute;
    box-shadow: -3px 3px 7px rgb(0 0 0 / 7%);
    transform: translateX(4.24264069px) rotate(45deg);
    top: 0;
    bottom: 0;
    left: -8px;
    display: block;
    width: 8px;
    height: 8px;
    margin: auto;
    background-color: #fff;
    content: "";
    pointer-events: auto;
  }
}
</style>
<style scoped lang="scss">
.hover_book {
  display: flex;
  align-items: center;
  background-color: #0375de !important;
  border-radius: 2px;
  padding: 5px;

  i {
    display: inline-block;
    cursor: pointer;
  }

  .del {
    width: 14px;
    height: 14px;
    background: url("../assets/img/icon/book_delete.png") no-repeat center center;
    background-size: 100% 100%;
  }

  .rol {
    width: 12px;
    height: 15px;
    background: url("../assets/img/icon/book_authority.png") no-repeat center center;
    background-size: 100% 100%;
    margin: 0 7px;
  }

  .remove {
    width: 11px;
    height: 11px;
    background: url("../assets/img/icon/book_del.png") no-repeat center center;
    background-size: 100% 100%;
  }
}

.newInputWz {
  top: -18px !important;
}

.leftNewInputWz {
  left: -231px !important;
  right: auto !important;

  &::before {
    left: auto !important;
    right: 0 !important;
  }
}

.delete_book {
  padding: 10px;
  padding-top: 0;
  text-align: left;

  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: normal;
    margin-bottom: 5px;
    line-height: 1;
    color: #333;
  }
}

.delete_icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url("../assets/img/icon/delete_icon.png") no-repeat center center;
  background-size: 100% 100%;
  cursor: pointer;
  margin-left: 4px;
}

.edit_ctrl {
  border: 1px solid #ddd;
  box-shadow: 0 0 5px rgb(0 0 0 / 15%);
  margin-top: 5px;
}
.checkOk{
  display: inline-block;
  margin-top: 6px;
  width: 18px;
  height: 18px;
  background: url('../assets/img/check.png') no-repeat center center;
  background-size: 100% 100%;
  cursor: pointer;
}
</style>
