import request from '@/utils/request';
import qs from 'qs';
import store from '@/store';
export default class {
  // 超管权限用户
  static getUserAuth() {
    return request({
      url: '/bookcase/user_auth',
      method: 'get',
    });
  }
  // 当前登录用户
  static getUserInfo() {
    return request({
      url: '/bookcase/get_user',
      method: 'get',
    });
  }
  //   获取书架
  static getBookCase(isAdmin: any, params: any) {
    return request({
      url: isAdmin ? '/bookcase/adminRecycleBin' : '/bookcase/commonRecycleBin',
      method: 'post',
      data: params,
    });
  }

  // 撤回 彻底删除  dataDelFlag 0撤回 1彻底删除
  static file_handle(params: any) {
    return request({
      url: '/fileRecycle/updateSome',
      method: 'get',
      params,
    });
  }

  // 清空文件夹
  static file_clear(params: any) {
    return request({
      url: '/fileRecycle/clearAll',
      method: 'get',
      params,
    });
  }
  // 获取文件
  static find_fileFolders(params: any) {
    return request({
      url: '/fileRecycle/find_fileFolders',
      method: 'get',
      params: {
        ...params,
        bookshelfId: store.state.selectFile.uuid,
        bookcaseId:store.state.bookCase.uuid
      },
    });
  }
  // 修改书柜
  static bookcaseUpdate(params: any) {
    return request({
      url: '/bookcase/update',
      method: 'post',
      data: params,
    });
  }

  // 新查询回收站接口
  static findRecycleBin(params: any) {
    return request({
      url: 'bookcase/findRecycleBin',
      method: 'get',
      params,
    });
  }
}
