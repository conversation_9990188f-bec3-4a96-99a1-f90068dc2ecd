{"name": "app", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@ztree/ztree_v3": "^3.5.48", "ant-design-vue": "^3.0.0-alpha.14", "axios": "^0.24.0", "core-js": "^3.6.5", "dayjs": "^1.10.7", "element-plus": "^1.2.0-beta.5", "install": "^0.13.0", "qingstor-sdk": "^3.1.1", "swiper": "^7.3.1", "uglifyjs-webpack-plugin": "^2.2.0", "vue": "^3.0.0", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0", "webpack": "^4.46.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-service": "~4.5.0", "compression-webpack-plugin": "^5.0.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "typescript": "^4.1.5"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}