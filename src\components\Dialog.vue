<template>
  <div class="dialog-container" :style="{
      top: top,
      left: left,
      width: width,
      height: height,
      right: right,
    }" v-if="params.visible" v-drag>
    <div v-if="lock" class="mask"></div>
    <div class="title">
      <span>{{ title }}</span>
      <i class="close-dialog" @click="_close" />
      <i class="size-icon" v-if="max" />
    </div>
    <div :id="id" class="content">
      <slot></slot>
    </div>
    <div class="btn" style="display: none">
      <p class="submit" v-if="ok" @click="_ok">确定</p>
      <p class="cancel" v-if="close" @click="_close">关闭</p>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive } from "vue";
export default defineComponent({
  props: {
    top: {
      type: String,
    },
    left: {
      type: String,
    },
    right: {
      type: String,
    },
    width: {
      type: String,
    },
    height: {
      type: String,
    },
    lock: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: "标题",
    },
    max: {
      type: Boolean,
      default: false,
    },
    ok: {
      type: Boolean,
      default: false,
    },
    close: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
    },
  },
  setup() {
    let params = reactive({
      visible: false,
    });
    const _ok = () => {
      _close();
    };
    const _close = () => {
      params.visible = false;
    };
    return {
      visible: params.visible,
      _ok,
      _close,
    };
  },
});
</script>
<style lang="css" scoped>
.dialog-container * {
  margin-bottom: 0;
  margin: 0;
}
.dialog-container {
  display: inline-block;
  position: fixed;
  background: #fff;
  z-index: 100033;
  padding-top: 30px;
}
.dialog-container div:not(.title) {
  background: inherit;
}
.dialog-container .mask {
  width: 100%;
  height: 100%;
  background: #333 !important;
  opacity: 0.6;
  position: fixed;
  left: 0px;
  top: 0px;
  z-index: -1;
}
.title {
  text-align: left;
  padding: 0 10px;
  color: #fff;
  cursor: move;
  height: 30px;
  line-height: 30px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-image: none;
  background: #3080f8;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}
.title span {
  color: #fff;
  font-weight: 700;
  text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.7);
}
.content {
  height: 100%;
  overflow: auto;
}
.btn {
  text-align: right;
  padding: 10px;
}
.btn p {
  display: inline-block;
  height: 30px;
  padding: 0 10px;
  border: none;
  line-height: 30px;
  cursor: pointer;
  color: #fff;
  border-radius: 5px;
}
.btn p.submit {
  background: #3161a9;
  margin-right: 10px;
}
.btn p.cancel {
  background: #3161a9;
}
.close-dialog {
  cursor: pointer;
  float: right;
  position: relative;
  width: 20px;
  height: 20px;
  background: url("../assets/img/icon2.png") no-repeat;
  background-position: 0 -72px;
  top: 5px;
}
.close-dialog:hover {
  background-position: 0 -92px;
}
.size-icon {
  cursor: pointer;
  float: right;
  position: relative;
  width: 20px;
  height: 20px;
  background: url("../assets/img/icon2.png") no-repeat;
  background-position: 0 -152px;
  top: 5px;
  margin-right: 5px;
}
.size-icon.full {
  background-position: 0 -112px;
}
.size-icon.full:hover {
  background-position: 0 -132px;
}
.size-icon:hover {
  background-position: 0 -172px;
}
</style>
