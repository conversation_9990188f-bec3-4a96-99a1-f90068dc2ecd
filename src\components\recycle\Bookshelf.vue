<template>
  <!-- Slider main container -->
  <div class="swiper book_shelf">
    <div class="swiper-wrapper">
      <!-- 书格  超过五层上下轮播 -->
      <div class="swiper-slide" v-for="(items, index) in data" :key="items.uuid">
        <div class="layer">
          <!-- <span class="floor_num"
            ><span>{{ index + 1 }}</span
            >/5</span
          > -->
          <span class="folder_name" :title="items.shelfName">{{
            items.shelfName
          }}</span>
          <span class="user" v-if="items.isLabel!==1" title="书格主人">{{ items.userName }}</span>
        </div>
        <div class="book_item">
          <!-- 文件夹  -->
          <div class="folder_item" id="folder_item" v-for="(item, index) in items.fileFolderVos" :key="item.uuid" :style="{ zIndex: items.fileFolderVos.length - index }">
            <!-- <div class="isNotAdmin mask"></div> -->
            <div class="folder" @click="handleSelectFolder(item, items)" :class="{
                active: selectFolder.uuid == item.uuid,
                isNotAdmin: !isAdmin && userInfo.id !== items.userId,
                onlyFile: !item.dataDelFlag,
              }" :style="{backgroundColor:item.colorStr}">
              <div class="folder_name" @click.stop="editFileName(item,items)">
                <div class="scroll_name" @mouseleave="floderNameMouseOut" @mouseenter.self="floderNameMouseEnter">
                  <!-- <div>{{ item.fileName }}</div> -->
                  <div class="name" @mouseleave="floderNameMouseOut" @mouseenter.self="floderNameMouseEnter">
                    <span v-for="(itemx,indexx) in item.fileName" :key="indexx">{{itemx}}</span>
                  </div>
                </div>
              </div>
              <!-- <a-popover v-if="
                  (isAdmin || userInfo.id == items.userId) && item.dataDelFlag
                " color="#0375de" v-model:visible="item.visible" trigger="click" placement="top">
                <template #content>
                  <div class="hover_book">
                    <i class="del" title="彻底删除" @click="suerDelete(items, item, index)"></i>
                    <i class="rol" title="恢复" @click="returnFile(item)"></i>
                    <i class="remove" @click="closeBook(item)" title="关闭"></i>
                  </div>
                </template>
                <div class="folder_num" @click.stop="
                    (item) => {
                      reset();
                      item.visible = true;
                    }
                  ">
                  {{ item.fileFolderCounts }}
                </div>
              </a-popover> -->
              <div class="folder_num">
                <span :class="(item.fileFolderCounts).toString().length>=3 ?'tranformStyle':''" style="display: inline-block;">{{item.fileFolderCounts <= 999 ? item.fileFolderCounts : '999+'}}</span>
              </div>
            </div>
            <!-- 文件名弹框 -->
            <!-- (isAdmin || userInfo.id == items.userId) && item.dataDelFlag -->
            <div class="newInput" v-if="(isAdmin || userInfo.id == items.userId) && item.dataDelFlag && item.edit " @click.stop="" :class="{'newInputWz':indexs==4 ,'leftNewInputWz':index>=22,'postionRight':!isAdmin} ">
              <div class="newInput_top">
                <div class="hover_book">
                  <i class="del" v-if="isAdmin" title="彻底删除" @click="suerDelete(items, item, index)"></i>
                  <i class="rol" title="恢复" @click="returnFile(item)"></i>
                  <!-- <i class="remove" @click="closeBook(item)" title="关闭"></i> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  nextTick,
  ref,
  defineProps,
  reactive,
  watch,
  computed,
  inject,
  onMounted,
  onUnmounted,
  defineExpose
} from "vue";
import indexApi from "@/api/recycle";
import { Modal } from "ant-design-vue";
import { ElMessage as message } from "element-plus";
import { useStore } from "vuex";
import utils from "@/utils/utils";
const store = useStore();
import $ from "jquery";
const getUserAuth = inject("getUserAuth");
const { data } = defineProps({
  data: {
    type: Object,
  },
});
const isAdmin = computed(() => store.state.isAdmin);
const userInfo = computed(() => store.state.userInfo);
/**
 * 2书格
 * 3书
 */
let type = ref(2);
// 新建文件夹名称
let folderName = ref();
const dataList = ref([]);
// 双击事件定时器
let clickTime: any = null;
// 重置状态
const reset = () => {
  data.forEach((item) => {
    item.add = false;
    item.editName = false;
    item.fileFolderVos?.forEach((items) => {
      items.edit = false;
      items.visible = false;
    });
  });
};
onMounted(() => {
  document.addEventListener("click", (e) => {
    if (!$(e.target).closest(".folder_name,.ant-input").length) {
      reset();
    }
    if (
      $(e.target).closest(".book_item").length &&
      !$(e.target).closest(".folder,.folder_items").length
    ) {
      store.commit("setClear");
      store.commit("setSelectFolder", {});
    }
  });
});
onUnmounted(() => {
  document.removeEventListener("click", () => {});
});
const selectFolder = computed(() => store.state.selectFolder);
for (let i = 0; i < 10; i++) {
  dataList.value.push({
    visible: false,
  });
}
let swiper:any=ref();
defineExpose({
  swiper,
  type
})
nextTick(() => {
  swiper.value = new Swiper(".book_shelf", {
    direction: "vertical",
    slidesPerView: 5,
    nested: true,
    loop: false,
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
  });
});
// 选中书架文件夹
const handleSelectFolder = (val: object, items: object) => {
  store.commit("setSelectFolder", val);
  store.commit("setSelectFile", items);
  store.commit("setRightSelectFolder", {});
  store.commit("setAsyncFolder", {});
};
// 文字滚动定时器
let timer: any = null;
// 鼠标移入文件夹名称滚动动画
const floderNameMouseEnter = (e: any) => {
  if (timer) {
    clearInterval(timer);
  }
  let scrollHeight = $(e.target)[0].scrollHeight;
  let offsetHeight = $(e.target)[0].offsetHeight;
  if (scrollHeight > offsetHeight) {
    timer = setInterval(() => {
      $(e.target).animate(
        {
          marginTop: "-=1",
        },
        0,
        function () {
          var s = Math.abs(parseInt($(e.target).css("margin-top")));
          if (s >= scrollHeight - offsetHeight) {
            $(e.target).find("div").slice(0, 1).appendTo($(e.target));
            $(e.target).css("margin-top", 0);
          }
        }
      );
    }, 80);
  }
};
// 鼠标移出文件夹名称
const floderNameMouseOut = (e: any) => {
  clearInterval(timer);
  if ($(e.target).hasClass("scroll_name")) {
    $(e.target).children(".name").css("margin-top", 0);
    $(e.target).css("margin-top", 0);
  }
};
// 打开新建书本弹框
const openAdd = (val: any) => {
  reset();
  val.add = true;
};
// 新建书本文件夹保存
const hideAdd = async (val: any) => {
  if (!folderName.value) return message.warning("请输入书本名称!");
  let params = {
    uuid: utils.uuid(),
    fileName: folderName.value,
    type: "folder",
    bookcaseId: store.state.bookCase.uuid,
    shelfId: val.uuid,
    level: 0,
  };
  const { data, code, msg } = await indexApi.fileFolderAdd(params);
  if (code == 200) {
    folderName.value = "";
    getUserAuth();
  } else {
    message.warning(msg);
  }
  val.add = false;
};
// 配置人员参数
let settingUserParams = reactive({
  visible: false,
  title: "权限配置",
  alert: "请配置书格的查看权限",
  // 书格id
  uuid: "",
  // 书id
  fileUuid: "",
});
//恢复
const returnFile = (item: any) => {
  indexApi
    .file_handle({
      uuid: item.uuid,
      dataDelFlag: 0,
    })
    .then((res) => {
      if (res.code == 200) {
        getUserAuth();
      } else {
        message.warning(res.msg);
      }
    });
  return;
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "确定撤回",
    content: () => "确定撤回当前文件夹吗?",
    onOk() {
      indexApi
        .file_handle({
          uuid: item.uuid,
          dataDelFlag: 0,
        })
        .then((res) => {
          if (res.code == 200) {
            getUserAuth();
          } else {
            message.warning(res.msg);
          }
        });
    },
    onCancel() {},
  });
};
const closeSettingUser = () => {
  settingUserParams.visible = false;
};
// 关闭书本弹框
const closeBook = (val: any) => {
  val.visible = false;
};
const suerDelete = (vals: any, val: any, index: number) => {
  closeBook(val);
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "确定删除",
    content: () => "确定彻底删除该文件夹吗?",
    onOk() {
      indexApi
        .file_handle({
          uuid: val.uuid,
          dataDelFlag: 2,
        })
        .then((res) => {
          if (res.code == 200) {
            getUserAuth();
          } else {
            message.warning(res.msg);
          }
        });
    },
    onCancel() {},
  });
};
const editShelfName = (val) => {
  data.forEach((item) => {
    item.editName = false;
  });
  val.editName = true;
};
// 修改书格名称
const upDateShelfName = async (val) => {
  if (!val.shelfName) return message.warning("请输入书格名称!");
  let parmas = {
    bookcaseId: store.state.bookCase.uuid,
    uuid: val.uuid,
    shelfName: val.shelfName,
  };
  const { data, code, msg } = await indexApi.update(parmas);
  if (code == 200) {
    val.editName = false;
  } else {
    message.warning(msg);
  }
};
// 打卡修改书的名称弹框
const editFileName = (val, vals) => {
  clickTime = new Date().getTime();
  setTimeout(() => {
    if (new Date().getTime() - clickTime < 300) {
      if (isAdmin.value || userInfo.value.id == vals.userId) {
        reset();
        val.edit = true;
      }
    }
    handleSelectFolder(val, vals);
  }, 300);
};
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.newInput {
  position: absolute;
  right: -76px;
  top: 25px;
  padding: 6px;
  z-index: 99999;
  &::before {
    display: inline-block;
    position: absolute;
    box-shadow: -3px 3px 7px rgb(0 0 0 / 7%);
    transform: translateX(4.24264069px) rotate(45deg);
    top: 0;
    bottom: 0;
    left: 0;
    display: block;
    width: 8px;
    height: 8px;
    margin: auto;
    background-color: #0375de;
    content: "";
    pointer-events: auto;
  }
}
.swiper {
  width: 100%;
  height: 100%;
  margin: 0;
  .swiper-slide {
    position: relative;
    display: flex;
    align-items: flex-end;
    background-color: rgba(0, 0, 0, 0.3);
    height: 100%;
    background: url("../../assets/img/background/bookshelf-recycle.png")
      no-repeat center center;
    background-size: 100% 100%;
    padding-left: 4.5%;
    padding-right: 3%;
    .layer {
      width: 22%;
      height: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: absolute;
      top: 0;
      left: 1%;
      .floor_num {
        font-size: $fontSize;
        color: #fff;
        > span {
          color: #13a6f3;
        }
      }
      .folder_name {
        display: inline-block;
        position: relative;
        font-size: $fontSize;
        color: #fff;
        text-align: left;
        padding-left: 10px;
        // margin-left: 8px;
        width: 130px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        line-height: 20px;
        background-color: #6e7279;
        border-radius: 4px;
        cursor: default;
      }
      .user {
        font-size: $fontSize;
        color: #fff;
        margin: 0 10px;
      }
    }
  }
  .book_item {
    display: flex;
    width: 100%;
    height: 100%;
    white-space: nowrap;
    align-items: flex-end;
    // overflow-x: auto;
    padding-bottom: 1.2%;

    .active {
      position: relative;
      top: -12px;
      transform: scale(1.2);
    }
    .folder_item {
      position: relative;
      display: inline-block;
      flex: 0 0 27px;
      height: 70%;
      margin-right: 4px;

      // &:nth-child(3n + 1) {
      .folder {
        background-color: #72349d;
        // background: linear-gradient(270deg, #0049e7 0%, #5086ff 100%);
      }
      // }
      // &:nth-child(3n - 1) {
      //   .folder {
      //     background: linear-gradient(270deg, #0090ed 0%, #36ceff 100%);
      //   }
      // }
      // &:nth-child(3n) {
      //   .folder {
      //     background: linear-gradient(270deg, #fc9100 0%, #ffae41 100%);
      //   }
      // }

      // &:nth-child(3n + 1) {

      // }
      // &:nth-child(3n - 1) {
      //   .folder.isNotAdmin {
      //     background: linear-gradient(270deg, #2b7b97 0%, #02588e 100%);
      //   }
      // }
      // &:nth-child(3n) {
      //   .folder.isNotAdmin {
      //     background: linear-gradient(270deg, #996825 0%, #975803 100%);
      //   }
      // }

      .onlyFile {
        background: linear-gradient(
          270deg,
          #474f5b 0%,
          #313843 100%
        ) !important;
        box-shadow: 0px 0px 2px #fff;
      }

      .onlyFile.isNotAdmin {
        // background: linear-gradient(270deg, #2e4f98 0%, #022d8b 100%);
        background-color: #72349d !important;
      }
    }
    .folder {
      position: relative;
      border-radius: 3px;
      width: 100%;
      height: 100%;
      cursor: pointer;
      transition: 0.2s transform ease-in-out;
      overflow: hidden;
      &:hover {
        transform: scale(1.2);
      }

      .folder_name {
        position: relative;
        width: 74%;
        height: 65%;
        background-color: #fff;

        font-size: $fontSize;
        text-align: center;
        border-radius: 2px;
        // writing-mode: vertical-lr;
        margin: 0 auto;
        margin-top: 10px;
        overflow: hidden;
        .scroll_name {
          display: flex;
          flex-direction: row;
          height: 100%;
          width: 100%;
          div {
            display: flex;
            flex-direction: column;
            width: 100%;
            align-items: center;
            font-size: $fontSize;
            color: #666666;
            // line-height: 19px;
          }
          // -webkit-animation: 10s rowup linear infinite normal;
          // animation: 10s rowup linear infinite normal;
          // animation: move 5s infinite alternate linear;
        }
      }
      .folder_num {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        margin: 0 auto;
        border-radius: 50%;
        font-size: $fontSize;
        margin-top: 20%;
        background-color: #fff;
        color: #666666;
      }
    }
  }
}
.isNotAdmin {
  opacity: 0.7;
}
.mask {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
</style>
<style scoped lang="scss">
.hover_book {
  display: flex;
  align-items: center;
  background-color: #0375de !important;
  border-radius: 2px;
  padding: 5px;
  i {
    display: inline-block;
    cursor: pointer;
  }
  .del {
    width: 20px;
    height: 20px;
    background: url("../../assets/img/icon/book_delete.png") no-repeat center
      center;
    background-size: 100% 100%;
  }
  .rol {
    width: 20px;
    height: 16px;
    background: url("../../assets/img/icon/book_recovery.png") no-repeat center
      center;
    background-size: 100% 100%;
    margin: 0 7px;
  }
  .remove {
    width: 11px;
    height: 11px;
    background: url("../../assets/img/icon/book_del.png") no-repeat center
      center;
    background-size: 100% 100%;
  }
}
.user {
  display: inline-block;
  line-height: 24px;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.postionRight {
  right: 51px !important;
}
</style>
