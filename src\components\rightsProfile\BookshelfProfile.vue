<template>
  <a-modal :maskClosable="false" width="650px" style="left: 109px;position:absolute" :footer="null" :visible="visible" title="书柜权限配置" @cancel="close" @update:visible="$emit('update:visible', $event)">
    <div class="content">
      <!-- <div class="title">请对该书架的权限进行配置</div> -->
      <!-- <table>
        <colgroup>
          <col width="100px" />
          <col width="77px" />
          <col />
        </colgroup>
        <tbody>
          <tr>
            <td rowspan="2">
              <span class="button" @click="handleCheck(1)">查看</span>
            </td>
            <td>部门</td>
            <td>
              <template v-for="(item,index) in detpList" :key="item.uuid">
                {{item.name +(index+1<detpList.length ?'、':'')}}
              </template>
            </td>
          </tr>
          <tr>
            <td>人员</td>
            <td>
              <template v-for="(item,index) in userList" :key="item.uuid">
                {{item.name +(index+1<userList.length ?'、':'')}}
              </template>
            </td>
          </tr>
          <tr>
            <td colspan="2" :rowspan="Math.ceil(shelfListOne.length/3)">
              <span class="button" :class="!userDeptList.length ? 'noRole' :''" @click="handleCheck(2)">书格主人</span>
            </td>
            <td>
              <span v-for="(item,index) in shelfList[0]" :key="item.uuid">{{`第${Arabia_To_SimplifiedChinese(item.bookcaseNum)}层:${item.userName ? item.userName :''} `}}</span>
            </td>
          </tr>
          <template v-for="(item,index) in shelfList" :key="index">
            <tr v-if="index">
              <td><span v-for="(items,indexs) in item" :key="items.uuid">{{`第${Arabia_To_SimplifiedChinese(items.bookcaseNum)}层:${items.userName ? items.userName :''} `}}</span></td>
            </tr>
          </template>
        </tbody>
      </table> -->
      <div class="aside">
        <div class="aside_left">
          <!-- <div class="aside_title">请选择人员或部门</div> -->
          <ul class="list ztree" id="trees">

          </ul>
        </div>
        <div class="aside_right">
          <div class="aside_right_top">
            <div class="aside_title">拥有书柜使用权限的人员或部门</div>
            <ul class="list">
              <li v-for="(item,index) in userDeptList" :key="item.uuid">
                <div class="li_left">
                  <i class="type_icon" v-if="item.type!='dept'"></i>
                  <i class="dept_icon" v-else></i>
                  <span :title="item.name">{{item.name}}</span>
                </div>
                <i class="delete_icon" @click="deleteUser_Dept(item)"></i>
              </li>
            </ul>
          </div>
          <div class="aside_right_bottom" v-if="false">
            <div class="aside_title">书格主人</div>
            <div class="list">
              <div v-for="(item,index) in floorList" :key="index" class="item">
                <div class="label">第{{Arabia_To_SimplifiedChinese(index+1)}}层</div>
                <div class="right_input">
                  <a-input readonly v-model:value="item.userName" @click.stop="handleOk(item,index)" />
                  <ul v-if="item.showUserList" :id="'ztree_'+index" class="ztree">
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="delete_alert">
        <a-switch @change="notice" :checkedValue="'Y'" :unCheckedValue="'N'" v-model:checked="noticeResult" checked-children="开" un-checked-children="关" />
        <span>
          <span class="delete_title">删除提醒</span>
          <span class="delete_content"><i class="icon"></i>开启删除提醒后,团队成员在进行文件删除后,您将收到提醒</span>
        </span>
      </div>
    </div>

    <div class="footer">
      <div class="btn" @click="close">确定</div>
    </div>
  </a-modal>
  <SettingUser :type="1" v-if="settingUserParams.visible" :userDeptList="userDeptList" :getList="findUserDept" :params="settingUserParams" @close="closeSettingUser"></SettingUser>
  <BookOwner :findShelfAdmin="findShelfAdmin" v-if="bookOwnerParams.visible" :shelfListOne="shelfListOne" :params="bookOwnerParams"></BookOwner>
</template>
<script setup lang="ts">
import {
  ref,
  defineProps,
  defineEmits,
  reactive,
  computed,
  inject,
  onMounted,
  nextTick,
} from "vue";
import settingApi from "@/api/setting";
import SettingUser from "./SettingUser.vue";
import BookOwner from "./BookOwner.vue";
import commonHooks from "@/hooks/commonHooks";
import indexApi from "@/api/Index";
import { useStore } from "vuex";
const { Arabia_To_SimplifiedChinese } = commonHooks();
const store = useStore();
const bookCase = computed(() => store.state.bookCase);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
});
const emit = defineEmits(["close", "update:visible"]);
const getUserAuth = inject("getUserAuth");
// 选中部门/人员
let userDeptList = ref([]);
let userList = ref([]);
let detpList = ref([]);
// 书格一维数组
let shelfListOne = ref([]);
// 书格二维数组
let shelfList = ref([]);
// 配置人员参数
let settingUserParams = reactive({
  visible: false,
  title: "配置人员",
  alert: "",
});
// 配置书格主人参数
let bookOwnerParams = reactive({
  visible: false,
});
let value = ref(0);
const floorList: any = ref([]);

const close = () => {
  getUserAuth();
  emit("close", false);
  emit("update:visible", false);
};
/**
 * 1 查看书架权限
 * 2 书格配置权限
 */
const handleCheck = (val) => {
  switch (val) {
    case 1:
      settingUserParams.visible = true;
      break;
    case 2:
      if (!userDeptList.value.length) return;
      bookOwnerParams.visible = true;
      break;
    default:
      break;
  }
};
const closeSettingUser = () => {
  settingUserParams.visible = false;
};
// 获取权限
const findUserDept = async () => {
  const { data, code, msg } = await settingApi.findUserDept({
    bookcaseId: bookCase.value.uuid,
  });
  if (code == 200) {
    userDeptList.value = data;
  }
};
findUserDept();
// 获取书格主人
const findShelfAdmin = async () => {
  const { data, code, msg } = await settingApi.findShelfAdmin({
    bookcaseId: bookCase.value.uuid,
  });
  if (code == 200) {
    let arr: any = data.sort((a, b) => {
      return a.bookcaseNum - b.bookcaseNum;
    });
    shelfListOne.value = arr;
    for (let i = 0; i < shelfListOne.value.length; i++) {
      floorList.value.push({
        userId: shelfListOne.value[i].userId,
        userName: shelfListOne.value[i].userName,
        bookcaseId: store.state.bookCase.uuid,
        bookcaseNum: i + 1,
        showUserList: false,
      });
    }
    shelfList.value = [];
    for (let i = 0; i < data.length; i += 3) {
      shelfList.value.push(arr.slice(i, i + 3));
    }
  } else {
  }
};
findShelfAdmin();

let zTree = null;

// 新增用户部门
const insertUser_Dept = async (val: any) => {
  let paramsObj = {
    [val.type == "dept" ? "deptName" : "userName"]: val.name,
    bookcaseId: store.state.bookCase.uuid,
  };
  paramsObj[val.type == "dept" ? "bookcaseDeptId" : "bookcaseUserId"] = val.id;
  const { data, code, msg } = await settingApi.insertUser_Dept(
    paramsObj,
    val.type == "dept" ? 1 : 0
  );
  if (code == 200) {
    let userDept = {
      name: val.name,
      id: val.id,
      uuid: data,
      type: val.type,
    };
    if (val.type == "dept") {
      let index = userDeptList.value.findIndex((item) => {
        return item.type !== "dept";
      });
      userDeptList.value.splice(index, 0, userDept);
    } else {
      userDeptList.value.push(userDept);
    }
  } else {
    message.warning(msg);
  }
};
// 取消页面选中
const deletePageSelect = (val) => {
  let node = zTree.getNodeByParam("id", val.id);
  if (node != null) {
    zTree.checkNode(node, false); //将指定ID的节点不选中
  }
  let index = userDeptList.value.findIndex((item) => {
    return item.uuid == val.uuid;
  });
  userDeptList.value.splice(index, 1);
};
// 删除用户部门
const deleteUser_Dept = async (val: any) => {
  let params = {
    uuid: val.uuid,
  };
  const { data, code, msg } = await settingApi.deleteUser_Dept(
    params,
    val.type == "dept" ? 1 : 0
  );
  if (code == 200) {
    deletePageSelect(val);
  } else {
    message.warning(msg);
  }
};
let ZTreeSetting = {
  async: {
    enable: true,
    autoParam: ["id"],
    type: "get",
    url:
      process.env.NODE_ENV === "development"
        ? "/QSecurity/getUserAndDept"
        : "/jchc-edisk/QSecurity/getUserAndDept",
    dataFilter: function (treeId, parentNode, res) {
      var childNodes = res.data;
      res.data.forEach((item: any) => {
        if (
          userDeptList.value &&
          userDeptList.value.findIndex((items) => {
            return items.id == item.id;
          }) !== -1
        ) {
          item.checked = true;
        }
      });
      return childNodes;
    },
  },
  check: {
    enable: true,
    chkboxType: { Y: "", N: "" },
  },
  data: {
    key: {
      title: "name",
    },
    simpleData: {
      enable: true,
      idKey: "id",
      pIdKey: "Pid",
      rootPId: "-1",
    },
  },
  callback: {
    onCheck: function (event, zTreeId, node) {
      if (node.checked) {
        insertUser_Dept(node);
      } else {
        let el = userDeptList.value.find((item) => {
          return item.id == node.id;
        });
        deleteUser_Dept(el);
      }
    },
  },
};
const initZtree = () => {
  zTree = $.fn.zTree.init($("#trees"), ZTreeSetting);
};

// 插入书格主人
const insertShelfAdmin = async () => {
  const { data, code, msg } = await settingApi.insertShelfAdmin(
    floorList.value[selectIndex.value]
  );
  if (code == 200) {
    rest();
  } else {
    message.warning(msg);
  }
};
let selectIndex = ref(0);
let zTree_item: any = null;
let ZTreeSettingItem = {
  async: {
    enable: true,
    autoParam: ["id"],
    otherParam: ["bookcaseId", store.state.bookCase.uuid],
    type: "get",
    url:
      process.env.NODE_ENV === "development"
        ? "/QSecurity/getUserAndDeptForShelf"
        : "/jchc-edisk/QSecurity/getUserAndDeptForShelf",
    dataFilter: function (treeId, parentNode, res) {
      var childNodes = res.data;
      res.data.forEach((item) => {
        if (floorList.value[selectIndex.value].userId == item.id) {
          item.checked = true;
        }
      });
      return childNodes;
    },
  },
  check: {
    chkboxType: {
      Y: "",
      N: "",
    },
    chkStyle: "radio",
    // radioType: "all",
    enable: true,
  },
  data: {
    simpleData: {
      enable: true,
      idKey: "id",
      pIdKey: "parentId",
      rootPId: "0",
    },
  },
  view: {
    showIcon: false,
  },
  callback: {
    onCheck: function (event, zTreeId, node) {
      if (node.checked) {
        floorList.value[selectIndex.value].userId = node.id;
        floorList.value[selectIndex.value].userName = node.name;
        insertShelfAdmin();
      } else {
        floorList.value[selectIndex.value].userId = "";
        floorList.value[selectIndex.value].userName = "";
        insertShelfAdmin();
      }
    },
  },
};
const rest = () => {
  zTree_item ? zTree_item.destroy() : "";
  floorList.value.forEach((item) => {
    item.showUserList = false;
  });
};
const handleOk = (item, index) => {
  rest();
  item.showUserList = true;
  selectIndex.value = index;
  nextTick(() => {
    zTree_item = $.fn.zTree.init($(`#ztree_${index}`), ZTreeSettingItem);
  });
};
let noticeResult = ref("N");
const notice = async () => {
  const { data, code, msg } = await indexApi.notice({
    uuid: bookCase.value.uuid,
    noticeResult: noticeResult.value,
  });
  console.log(data, code, msg);
};
// 获取当前书柜开关状态
const bookcaseFindOne = async () => {
  const { data, code, msg } = await indexApi.bookcaseFindOne({
    uuid: bookCase.value.uuid,
  });
  if (code == 200) {
    noticeResult.value = data.noticeResult;
  }
};
bookcaseFindOne();
onMounted(() => {
  initZtree();
  document.addEventListener("click", (e) => {
    if (!$(e.target).closest(".ztree, .ant-input").length) {
      floorList.value.forEach((item) => {
        item.showUserList = false;
      });
    }
  });
});
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.delete_alert {
  display: flex;
  align-items: center;
  border: 1px solid #b1c5e1;
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  > span {
    display: flex;
    align-items: center;
    margin-left: 20px;
    .delete_title {
      color: #3162a7;
      font-size: 14px !important;
      font-weight: bold;
      margin-right: 8px;
    }
    .delete_content {
      display: flex;
      align-items: center;
    }
    i {
      display: inline-block;
      width: 14px;
      height: 14px;
      background: url("../../assets/img/icon/icon_alert.png") no-repeat center
        center;
      background-size: 100% 100%;
      margin-right: 4px;
    }
  }
}
.label {
  margin-right: 14px;
}
.content {
  padding: 22px;
  padding-top: 18px;
  .title {
    margin-bottom: 17px;
    font-size: $fontSize;
    color: #333;
  }
  .button {
    display: inline-block;
    padding: 0 21px;
    height: 32px;
    line-height: 32px;
    border-radius: 3px;
    background-color: #3162a7;
    color: #fff;
    font-size: $fontSize;
    cursor: pointer;
  }
  table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    td {
      border: 1px solid #b1c5e1;
      height: 46px;
      background-color: #eaeff6;
      color: #333333;
      font-size: $fontSize;
      text-align: center;
    }
    tr {
      td {
        &:last-child {
          text-align: left;
          background-color: #fff;
          padding-left: 20px;
        }
      }
    }
  }
}
.noRole {
  background-color: #999 !important;
  cursor: not-allowed !important;
}
.aside {
  height: 400px;
  display: flex;
  .aside_left,
  .aside_right {
    flex: 1;
    width: 0;
  }
  .aside_title {
    font-size: $fontSize;
    color: #3162a7;
    height: 40px;
    line-height: 40px;
  }
  .aside_left,
  .aside_right_bottom,
  .aside_right_top {
    > .list {
      height: 100%;
      overflow: auto;
    }
  }
  .aside_left {
    margin-right: 10px;
    border: 1px solid #b1c5e1;
    background-color: #fff;
    padding: 9px;
    padding-top: 0;
  }
  .aside_right {
    .aside_right_top {
      height: 264px;
      height: 100%;
      margin-bottom: 10px;
      background-color: #fff;
      border: 1px solid #b1c5e1;
      padding: 9px;
      padding-top: 0;
      .list {
        li {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: $fontSize;
          // height: 27px;
          line-height: 27px;
          .li_left {
            display: flex;
            align-items: center;
            width: 90%;
          }
          .type_icon {
            display: inline-block;
            width: 13px;
            height: 15px;
            background: url("../../assets/img/icon/user_dept.png") no-repeat
              center center;
            background-size: 100% 100%;
            margin-right: 5px;
          }
          .dept_icon {
            display: inline-block;
            width: 11px;
            height: 11px;
            background: url("../../assets/img/icon/dept_icon.png") no-repeat
              center center;
            background-size: 100% 100%;
            margin-right: 5px;
            flex: 0 0 11px;
          }
          .delete_icon {
            display: inline-block;
            width: 14px;
            height: 16px;
            background: url("../../assets/img/icon/delete_icon.png") no-repeat
              center center;
            background-size: 100% 100%;
            cursor: pointer;
            flex: 0 0 14px;
          }
          span {
            display: inline-block;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
    .aside_right_bottom {
      height: calc(100% - 274px);
      background-color: #fff;
      padding: 9px;
      padding-top: 0;
      border: 1px solid #b1c5e1;
      .item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        .right_input {
          flex: 1;
          position: relative;
          ul {
            position: absolute;
            top: 33px;
            width: 100%;
            height: 140px;
            background-color: #fff;
            z-index: 99;
            border-radius: 3px;
            box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
            overflow: auto;
          }
        }
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.footer {
  overflow: hidden;
  padding-bottom: 22px;
  padding-right: 22px;
  .btn {
    float: right;
    background-color: #3162a7;
    color: #fff !important;
    width: 60px;
  }
}
</style>

