<template>
  <a-modal width="413px" :footer="null" v-model:visible="params.visible" title="新建标签" @cancel="close" :maskClosable="false">
    <div class="content newLabel">
      <div class="title">请对该文件进行标签设置</div>
      <div class="rates">
        <a-rate v-model:value="level" :count="3"></a-rate>
      </div>
      <div class="footer alert">
        <div class="ant-btn" @click="close">取消</div>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import {
  defineProps,
  ref,
  defineEmits,
  inject,
  onUnmounted,
  onMounted,
} from "vue";
import indexApi from "@/api/Index";
import { ElMessage as message } from "element-plus";

onUnmounted(() => {
  $(document).unbind();
});
const { params, type } = defineProps({
  params: {
    type: Object,
  },
  type: {
    type: Number,
  },
});
const emit = defineEmits(["close"]);
const editDataList = inject("editDataList");
let level = ref(params.level ? params.level : 0);
onMounted(() => {
  $(document).click(function (e: any) {
    if ($(e.target).hasClass("newLabel") || $(e.target).hasClass("rates")) {
      level.value = 0;
    }
  });
});
const close = () => {
  emit("close");
};
const handleOk = async () => {
  if (type == 1) {
    const { data, code, msg } = await indexApi.updateBatch({
      uuids: params.filterCheck.map((item) => {
        return item.uuid;
      }),
      type: "level",
      level: level.value,
    });
    if (code == 200) {
      params.filterCheck.forEach((item) => {
        item.level = level.value;
        item.check = false;
        editDataList(item, 1);
      });
      level.value = 0;
    } else {
      message.warning(msg);
    }
  } else {
    const { data, code, msg } = await indexApi.fileFolderUpdate({
      uuid: params.uuid,
      level: level.value,
    });
    if (code == 200) {
      params.level = level.value;
      editDataList(params, 1);
    } else {
      message.warning(msg);
    }
  }

  close();
};
</script>
<style lang="scss">
$fontSize: 12px;
.newLabel {
  height: 219px;
  padding: 27px 19px 35px 19px;
  .title {
    font-size: $fontSize;
  }
  .rates {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
  }
  .el-rate {
    display: inline-block;
    .el-rate__icon {
      font-size: $fontSize !important;
    }
  }
  .footer {
    display: flex;
    margin-top: 40px;
    justify-content: center;
    .ant-btn {
      color: #fff;
      margin-right: 10px;
    }
    .ant-btn-primary {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}
</style>