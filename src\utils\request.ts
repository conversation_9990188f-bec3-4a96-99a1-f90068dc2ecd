import axios from 'axios';
import utils from '@/utils/utils';
// const $ = require('jquery');
const Axios = axios.create({
  baseURL: process.env.NODE_ENV === "development" ?'':'/jchc-edisk/'
});
const fliterApi: any = ['/fileFolder/upload','/caseAccount/findAccountAdminByOrgs','/fileFolder/downloadBatch','/fileFolder/download'];
Axios.interceptors.request.use(
  (config: any) => {
    if (!fliterApi.includes(config.url.indexOf('?') === -1 ? config.url : config.url.split('?')[0]) && config.url.indexOf('/fileFolder/download')==-1) {
      $('.sjk-loading').show();
    }
    config.url = utils.rewrite(config.url);
    return config;
  },
  (error) => {
    return error;
  }
);
Axios.interceptors.response.use(
  (response) => {
    $('.sjk-loading').hide();
    return response.data;
  },
  (error) => {
    $('.sjk-loading').hide();
    return error;
  }
);
export default Axios;
