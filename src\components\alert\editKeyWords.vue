<template>
  <a-modal width="413px" :footer="null" v-model:visible="params.visible" title="关键词设置" @cancel="close" :maskClosable="false">
    <div class="content newLabel">
      <div class="title">请设置文件文件关键词</div>
      <el-input maxlength="50" size="small" v-model="keyword" @keypress.enter="handleOk" />
      <div class="footer alert">
        <div class="ant-btn" @click="close">取消</div>
        <a-button type="primary" @click="handleOk">确定 </a-button>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import { defineProps, ref, defineEmits, computed, inject } from "vue";
import indexApi from "@/api/Index";
import { ElMessage as message } from "element-plus";
const { params } = defineProps({
  params: {
    type: Object,
  },
});
const editDataList = inject("editDataList");
const emit = defineEmits(["close"]);
let keyword = ref("");
const close = (val) => {
  emit("close");
};
const handleOk = async () => {
  // if (!keyword.value) return message.warning("关键词不能为空!");
  const { data, code, msg } = await indexApi.fileFolderUpdate({
    uuid: params.uuid,
    keyword: keyword.value,
  });
  if (code == 200) {
    params.keyword = keyword.value;
    editDataList(params, 1);
  } else {
    message.warning(msg);
  }
  close();
};
</script>
<style lang="scss">
$fontSize: 12px;
.newLabel {
  height: 219px;
  padding: 27px 19px 35px 19px;
  .title {
    font-size: $fontSize;
  }
  .el-input {
    margin-top: 22px;
  }
  .footer {
    display: flex;
    margin-top: 40px;
    justify-content: center;
    .ant-btn {
      color: #fff;
      margin-right: 10px;
    }
    .ant-btn-primary {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}
</style>