<template>
  <div class="index">
    <div class="left_content">
      <common-header :type="1" @searchBook="searchBook">
        <template #left_title>
          <div class="title_logo"></div>
          <div class="count">
            <!-- 统计： -->
            <span class="item" v-for="(item, index) in bookCase.count" :key="index" :title="item.title">
              <i :style="{ backgroundImage: 'url(' + item.icon + ')' }"></i>
              <span style="display:inline-block;width:30px;flex:0 0 30px">{{ item.num }}</span>
            </span>
          </div>
          <div class="progress"
            :title="(() => { return (bookCase.totalSpaceAll / 1024 / 1024 / 1024).toFixed(2) + 'G/' + store.state.bookCase.allTotal + 'G' })()">
            <div class="remaining">
              剩余容量:<span>{{ (100 - percent()).toFixed(2) + '%' }}</span>
              <span class="add_btn" v-show="!searchParams.keyword && !searchParams.level"
                v-if="store.state.isAdmin && !bookcaseDisabled" @click="capacity(0)">扩容</span>
            </div>
            <div class="used"
              :style="{ width: ((Number((bookCase.totalSpaceAll / 1024 / 1024 / 1024 / store.state.bookCase.allTotal).toFixed(2)) >= 1 ? 1 : Number((bookCase.totalSpaceAll / 1024 / 1024 / 1024 / store.state.bookCase.allTotal).toFixed(2))) * 400) + 'px' }">
            </div>
          </div>
        </template>
      </common-header>
      <div class="book_content">
        <div :class="store.state.bookCase.index ? 'has_left_bg' : ''" class="left_bg"
          :style="{ cursor: (((searchParams.keyword || searchParams.level) && !store.state.bookCase.index) ? 'default' : 'pointer') }"
          @click="addBookCase(0)"></div>
        <div class="pre-btn swiper-btn" @click="currentSwiper(0)" v-if="!bookcaseDisabled && showPrev"></div>
        <div class="swiper book_case swiper-no-swiping">
          <div class="bookcasedisabled" v-if="bookcaseDisabled"></div>
          <div class="swiper-wrapper">
            <div class="swiper-slide" v-for="(item, index) in bookCaseList" :key="item.uuid">
              <book-case-com :ref="($event: any) => {
                setRef($event, index)
              }" :data="item.bookshelfVos" :key="item.uuid"></book-case-com>

              <!-- :key="() => { new Date().valueOf() }" -->
            </div>
          </div>
          <div class="downBtn">
            <!-- 新增按钮 -->
            <div class="add-shugui"
              @click="addBookShelf" v-if="store.state.isAdmin && !bookcaseDisabled"></div>
            <!-- 切换按钮 -->
            <div  class="next-btn" @click="currentSwiper(1)"
              v-if="!bookcaseDisabled && showNext">
            </div>
          </div>
        </div>
        <div
          :class="(bookCaseList.length && store.state.bookCase.index != bookCaseList.length - 1 && bookCaseList.length != 1) ? 'has_right_bg' : ''"
          class="right_bg"
          :style="{ cursor: (((searchParams.keyword || searchParams.level) && (store.state.bookCase.index == bookCaseList.length - 1)) ? 'default' : 'pointer') }"
          @click="addBookCase(1)"></div>
      </div>
      <div class="newBookCase" v-if="!bookCaseList.length && !searchParams.keyword && !searchParams.level">
        <span class="add_icon" @click="addNewBookCase">+</span>
        <div class="bookcase_bg"></div>

      </div>
    </div>
    <right-table ref="rights"></right-table>
    <selectAccount v-if="accountparams.visible" :params="accountparams" @close="closeAccount"></selectAccount>
  </div>
</template>
<script lang="ts" setup>
import { message, Modal } from "ant-design-vue";
import {
  nextTick,
  reactive,
  ref,
  computed,
  provide,
  onUnmounted,
  watch,
  createVNode,
  h,
} from "vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import indexApi from "@/api/Index";
import RightTable from "@/components/RightTable";
import CommonHeader from "@/components/CommonHeader";
import BookCaseCom from "@/components/BookCase";
import utils from "@/utils/utils";
import selectAccount from "@/components/alert/selectAccount";
import { ElMessage } from "element-plus";
const route = useRoute();
const store = useStore();
const bookCase = computed(() => store.state.bookCase);
const bookcaseDisabled = computed(() => store.state.disabled);
const bookCaseList: any = ref([]);
const searchParams: any = reactive({
  keyword: "",
  level: "",
});
provide("searchParams", searchParams);
let swiper: any;
let index: number;
let rights = ref();
const swiperBookCaseRefs: any = ref([])
const setRef = (el: any, index: string | number) => {
  if (el) {
    swiperBookCaseRefs.value[index] = el
  }
}
let bookCaseIndex = ref(0)
// 当前显示的书格索引
let activeIndex = ref(0)
// 是否显示向上按钮
let showPrev = computed(() => Number(activeIndex.value))
// 是否显示向下按钮
let showNext = computed(() => {
  if (bookCaseList.value.length) {
    return Number(activeIndex.value) + 5 < bookCaseList.value[store.state.bookCase.index].bookshelfVos.length
  }
})
let retrunSwiper = () => {
  return bookCaseList.value.length == 1 ? swiperBookCaseRefs.value[store.state.bookCase.index].exports.value.swiper : swiperBookCaseRefs.value[store.state.bookCase.index].exports.value.swiper[store.state.bookCase.index]
}
watch(bookCaseIndex, (newVal, oldVal) => {
  activeIndex.value = 0
  nextTick(() => {
    let swiper = retrunSwiper()
    swiper.update()
    swiper.slideTo(0, 0, false);
  })
}, {
  deep: true
})
// 删除书格
const deleteBookShelf = (index) => {
  bookCaseList.value[store.state.bookCase.index].bookshelfVos.splice(index, 1)
  nextTick(() => {
    let swiper = retrunSwiper()
    swiper.update()
    activeIndex.value = swiper.activeIndex
  })
}
provide('deleteBookShelf', deleteBookShelf)
// 新建书格
const addBookShelf = async () => {
  const bookshelfVos = bookCaseList.value[store.state.bookCase.index].bookshelfVos
  const params = {
    bookcaseId: bookCaseList.value[store.state.bookCase.index].uuid,
    uuid: utils.uuid(),
    bookcaseNum: Number(bookshelfVos[bookshelfVos.length - 1].bookcaseNum) + 1,
    shelfName: `书格名称${Number(bookshelfVos[bookshelfVos.length - 1].bookcaseNum) + 1}`,
    fileFolderVos: [],
    linkedUser: [],
    dataDelFlag: 0,
    hasChildren: false
  }
  const { data, code, msg } = await indexApi.bookshelfAdd(params)
  if (code == 200) {
    bookshelfVos.push(params)
    nextTick(() => {
      let swiper = retrunSwiper()
      swiper.update()
      swiper.slideTo(bookCaseList.value[store.state.bookCase.index].bookshelfVos.length + 1, 500, false);
      activeIndex.value = swiper.activeIndex
    })
  }

}
// 切换书格
const currentSwiper = (val: any) => {
  let swiper = retrunSwiper()
  if (val) {
    // 向下切换
    swiper.slideNext()
    activeIndex.value = swiper.activeIndex
  } else {
    // 向上切换
    swiper.slidePrev()
    activeIndex.value = swiper.activeIndex
  }
}
const getBookCase = async (val = false) => {
  searchParams.level === 0 ? (searchParams.level = "") : "";
  const { data, code, msg } = await indexApi.findBookCase(searchParams);
  if (code == 200) {
    if (data instanceof Array) {
      const counts = {
        fileCounts: 0,
        folderCounts: 0,
      };
      data.forEach((item, index) => {
        item.bookshelfVos.forEach(itemx=>{
          itemx.fileFolderVos.forEach(itemy=>{
            itemy.edit = false
          })
        })
        item.sort = index + 1;
        counts.fileCounts += item.fileCounts;
        counts.folderCounts += item.folderCounts;
        if (item.bookshelfVos.length < 5) {
          let num = 5 - item.bookshelfVos.length;
          for (let i = 0; i < num; i++) {
            item.bookshelfVos.push({ isLabel: 1, uuid: utils.uuid() });
          }
        }
      });
      console.log(data)
      bookCaseList.value = data;
      // 当前书柜如果不在第一个则切换到上传位置
      index = data.findIndex((item) => {
        return item.uuid == store.state.bookCase.uuid;
      });
      store.commit("setBookCase", index !== -1 ? data[index] : data[0]);
      // 书柜数量
      store.commit("setBookCaseNum", bookCaseList.value.length);
      // 文件夹、文件数量
      store.commit("setCount", counts);
    } else {
      bookCaseList.value = [];
    }
    nextTick(() => {
      if (swiper) {
        swiper.update();
        swiper.slideTo(index);
      } else {
        swiper = new Swiper(".book_case", {
          direction: "horizontal",
          centeredSlides: true,
          loop: false,
          on: {
            slideChangeTransitionStart: function () {
              bookCaseIndex.value= this.activeIndex
              store.commit("setBookCase", bookCaseList.value[this.activeIndex]);
            },
          },
        });
        swiper.update();
        swiper.slideTo(index);
      }
    });
  } else {
    message.warning(msg);
  }
};
const getUser = async () => {
  const { data, code, msg } = await indexApi.getUserInfo();
  if (code == 200) {
    store.commit("setUserInfo", data);
    let isAdmin = store.state.userAuth.some((item: { uid: any; }) => {
      return item.uid === data.account;
    });
    store.commit("setAdmin", isAdmin);
    getBookCase(isAdmin);
  } else {
    message.warning(msg);
  }
};
const getUserAuth = async () => {
  getUser();
};
watch(
  () => store.state.fileNum,
  (newVal, oldVal) => {
    if (newVal) {
      getUserAuth();
      rights.value.resetIs();
      // if(!store.state.asyncFolder.uuid || !store.state.rightSelectFolder.uuid){
      //   rights.value.initRightTable()
      // }
      const state = store.state;
      if (state.selectFolder.uuid == state.file.selectFolder.uuid) {
        if (
          state.rightSelectFolder?.uuid &&
          state.rightSelectFolder.uuid === state.file.rightSelectFolder?.uuid
        ) {
        } else {
          rights.value.initRightTable();
        }
      }
    }
  },
  { deep: true }
);

const clear = () => {
  store.commit("setClear");
  store.commit("setSelectFile", {});
  store.commit("setSelectFolder", {});
  // swiper ? swiper.slideTo(0) : "";
};
getUserAuth();
const searchBook = (val: any) => {
  searchParams.keyword = val.keyword;
  searchParams.level = val.level;
  clear();
  store.commit("clearBooCase");
  getUserAuth();
};
// 修改书格配置调用刷新接口
provide("getUserAuth", getUserAuth);
onUnmounted(() => {
  clear();
});
const percent = () => {
  return (
    (bookCase.value.totalSpaceAll /
      1024 /
      1024 /
      1024 /
      bookCase.value.allTotal) *
    100
  );
};
const progressColor = () => {
  return percent() > 90 ? "#ED1010" : "#13a6f3";
};

const accountparams = reactive({
  visible: false,
  accountList: [],
  isAccount: 0,
  bookcaseId: "",
});

const closeAccount = () => {
  accountparams.visible = false;
  accountparams.isAccount = 0;
  accountparams.bookcaseId = "";
};
const type = {
  0: "扩容",
  1: "新建书柜",
};
// 获取账号信息
const findAccountsByOrgs = async (val: any) => {
  return false
  const { data, code, msg } = await indexApi.findAccountsByOrgs();
  if (code) {
    if (data.length) {
      accountparams.accountList = data;
      accountparams.visible = true;
    } else {
      let icon = h(
        "i",
        {
          style: {
            display: "inline-block",
            borderRadius: "50%",
            width: "20px",
            heigh: "20px",
            lineHeight: "20px",
            textAlign: "center",
            backgroundColor: "#fdb619",
            color: "#fff",
            fontSize: "16px !important",
            marginRight: "4px",
            flex: "0 0 20px",
            height: "20px",
          },
        },
        "!"
      );

      let icon1 = h("i", {
        style: {
          display: "inline-block",
          borderRadius: "50%",
          width: "12px",
          heigh: "12px",
          marginRight: "4px",
          flex: "0 0 12px",
          height: "12px",
        },
        class: "dialog_alert",
      });

      let protitle1 = h(
        "div",
        {
          class: "dti_pro_title dti_v_title",
          style: {
            fontSize: "14px",
            width: "440px",
          },
        },
        "试用版：单个文件大小不超过100M，容量1G"
      );

      let protitle2 = h(
        "div",
        {
          class: "dti_pro_title dti_v_title",
          style: {
            fontSize: "14px",
            width: "440px",
          },
        },
        "专业版：单个文件大小无限制，容量按需申请"
      );
      let title = h(
        "div",
        {
          class: "dti_title",
          style: {
            fontSize: "16px",
          },
        },
        "您的公司暂无服务云账号，请联系管理员注册"
      );
      let oneRow = h(
        "div",
        {
          class: "dti_alert",
          style: {
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            color: "#666",
          },
        },
        [icon1, "(管理员：负责付费账号的管理和扩容、新建书柜的审批)"]
      );
      let twoRow = h(
        "div",
        {
          style: {
            color: "#333",
            fontSize: "14px !important",
          },
        },
        "如果您的公司暂无管理员，可联系数研院咨询详情"
      );

      let threeRow = h(
        "div",
        {
          style: {
            color: "#333",
            fontSize: "14px !important",
          },
        },
        "客服工作时间：工作日，9:00:-17:30"
      );
      let fourRow = h(
        "div",
        {
          style: {
            color: "#333",
            fontSize: "14px !important",
          },
        },
        "客服热线：025-83676888"
      );

      let contactInformation = h(
        "div",
        {
          class: "contactInformation",
          style: {
            marginTop: "14px",
          },
        },
        [twoRow, threeRow, fourRow]
      );

      let contents = h(
        "div",
        {
          style: {
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          },
        },
        [title, oneRow, protitle1, protitle2, contactInformation]
      );

      Modal.confirm({
        closable: true,
        keyboard: false,
        width: 550,
        maskClosable: true,
        class: "alert confirm noAdminAlert",
        okText: () => "关闭",
        cancelText: () => "取消",
        title: () => "提示",
        content: () => contents,
        onOk() { },
      });
      // const { data, code, msg } = await indexApi.findUserLabel();
      // if (code) {
      //   if (data == 500) {
      //     Modal.confirm({
      //       closable: true,
      //       keyboard: false,
      //       width: 315,
      //       maskClosable: true,
      //       class: "alert alerts com_users",
      //       okText: () => "确定",
      //       cancelText: () => "取消",
      //       title: () => "提示",
      //       content: () =>`您的公司暂无服务云账号，请联系管理员注册`,
      //       onOk() {},
      //     });
      //   } else {
      //     Modal.confirm({
      //       closable: true,
      //       keyboard: false,
      //       width: 315,
      //       maskClosable: true,
      //       class: "alert alerts com_users",
      //       okText: () => "确定",
      //       cancelText: () => "取消",
      //       title: () => "提示",
      //       content: () =>
      //         `您的公司暂无服务云账号\n若需注册服务账号请与下列人员联系\n盛奔宇：***********\n靳路路：***********`,
      //       onOk() {},
      //     });
      //   }
      // }
    }
  } else {
  }
};

/**
 *
 * @param val 0 扩容 1新建
 */
const capacity = async (val: number) => {
  if (searchParams.keyword || searchParams.level) return;
  let searchTask = {};
  if (!val) {
    searchTask.bookcaseId = store.state.bookCase.uuid;
    accountparams.bookcaseId = store.state.bookCase.uuid;
  } else {
    accountparams.bookcaseId = utils.uuid();
  }

  // 查询是否有待办表单
  const { data, code, msg } = await indexApi.applyStatus(searchTask);
  if (data) {
    Modal.confirm({
      closable: true,
      keyboard: false,
      width: 300,
      class: "alert",
      okText: () => "确定",
      cancelText: () => "取消",
      title: () => "提示",
      content: () => "管理员审核中，是否再次发送申请?",
      onOk() {
        let params = {};
        !val ? (params.bookcaseId = store.state.bookCase.uuid) : "";
        indexApi.clickPush(params).then((res) => {
          if (res.data == 1) {
            message.warning(res.msg);
          }
        });
      },
    });
  } else {
    accountparams.newBuild = val;
    if (!val) {
      /**
       * 扩容查询是否有关联账号
       */
      const { data, code, msg } = await indexApi.accountByCaseId({
        bookcaseId: store.state.bookCase.uuid,
      });
      if (code) {
        if (data.length) {
          accountparams.accountList = data;
          accountparams.visible = true;
          accountparams.isAccount = 1;
        } else {
          /**
           * 无关联账号
           */
          findAccountsByOrgs(val);
        }
      }
    } else {
      /**
       * 新建
       */
      findAccountsByOrgs(val);
    }
  }
};
const inserBookCase = async (val: number | undefined) => {
  if (searchParams.keyword || searchParams.level) return;
  let index = bookCaseList.value.findIndex((item: { userId: any; }) => {
    return item.userId == store.state.userInfo.id;
  });
  if (index == -1) {
    Modal.confirm({
      closable: true,
      keyboard: false,
      width: 300,
      class: "alert alerts",
      okText: () => "确定",
      cancelText: () => "取消",
      title: () => "新建书柜",
      content: () => "是否新建书柜？新建后您将获得1G试用容量",
      onOk() {
        indexApi
          .addBookCase({
            uuid: utils.uuid(),
            sort: val ? 0 : bookCaseList.value.length + 1,
          })
          .then((res) => {
            if (res.code == 200) {
              message.success("新建成功");
              if (val) {
                bookCaseList.value.unshift(res.data);
                bookCaseList.value.forEach((item: any[], index: number) => {
                  item.sort = index + 1;
                });
              } else {
                res.data.index = bookCaseList.value.length;
                bookCaseList.value.push(res.data);
              }
              store.commit("setBookCaseNum", bookCaseList.value.length);
              nextTick(() => {
                swiper.update();
                if (val) {
                  /**
                   * 轮播图向前插入动画失效
                   */
                  swiper.slideTo(2, 0, true);
                  swiper.slideTo(0, 0, true);
                } else {
                  swiper.slideNext();
                }
              });
            } else {
              message.warning(res.msg);
            }
          });
      },
      onCancel() { },
    });
  } else {
    capacity(1);
  }

  return;
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 300,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "新建书柜",
    content: () => "是否新建书柜?",
    onOk() {
      indexApi
        .addBookCase({
          uuid: utils.uuid(),
          sort: val ? 0 : bookCaseList.value.length + 1,
        })
        .then((res) => {
          if (res.code == 200) {
            message.success("新建成功");
            if (val) {
              bookCaseList.value.unshift(res.data);
              bookCaseList.value.forEach((item: any[], index: number) => {
                item.sort = index + 1;
              });
            } else {
              res.data.index = bookCaseList.value.length;
              bookCaseList.value.push(res.data);
            }
            nextTick(() => {
              swiper.update();
              if (val) {
                /**
                 * 轮播图向前插入动画失效
                 */
                swiper.slideTo(2, 0, true);
                swiper.slideTo(0, 0, true);
              } else {
                swiper.slideNext();
              }
            });
          } else {
            message.warning(res.msg);
          }
        });
    },
    onCancel() { },
  });
};
// 添加书柜
const addBookCase = (val: number) => {
  if (!store.state.bookCase.index) {
    if (val) {
      bookCaseList.value.length > 1 ? swiper.slideNext() : inserBookCase();
    } else {
      inserBookCase(1);
    }
  } else if (store.state.bookCase.index == bookCaseList.value.length - 1) {
    val ? inserBookCase() : swiper.slidePrev();
  } else {
    val ? swiper.slideNext() : swiper.slidePrev();
  }
};

const updateName = (val: any) => {
  let bookcase = bookCaseList.value.find((item: { uuid: any; }) => {
    return item.uuid == store.state.bookCase.uuid;
  });
  bookcase.bookcaseName = val;
  store.commit("setBookCase", bookcase);
};
const addNewBookCase = () => {
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 300,
    class: "alert alerts",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "新建书柜",
    content: () => "是否新建书柜？新建后您将获得1G试用容量",
    onOk() {
      indexApi
        .addBookCase({
          uuid: utils.uuid(),
          sort: 1,
        })
        .then((res) => {
          if (res.code == 200) {
            res.data.index = bookCaseList.value.length;
            res.data.sort = 1;
            bookCaseList.value.push(res.data);
            store.commit("setBookCase", res.data);
            store.commit("setBookCaseNum", bookCaseList.value.length);
          } else {
            message.warning(res.msg);
          }
        });
    },
    onCancel() { },
  });
};

provide("updateName", updateName);
</script>
<style lang="scss" scoped>
.bookcasedisabled {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  background-color: rgba(218, 218, 219, 0.35);
  z-index: 999;
  cursor: no-drop;
}

.add_btn {
  display: inline-block;
  padding: 2px 4px;
  border-radius: 4px;
  margin-left: 10px;
  background-color: #3161a9;
  cursor: pointer;
}

.bookcase_bg {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  height: calc(100% - 60px);
  width: 85%;
  background: url("../assets/img/background/boocase_bg.png") no-repeat center center;
  background-size: 100% 100%;
}

.newBookCase {
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  // background-color: rgba(255, 255, 255, 0.6);
  z-index: 999;
  cursor: no-drop;

  .add_icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 100px;
    background-color: #fff;
    border-radius: 50%;
    color: #3161a9;
    font-size: 80px !important;
    cursor: pointer;
    z-index: 22;
  }
}

$fontSize: 12px;

.index {
  display: flex;
  height: 100%;
  width: 100%;

  .left_content {
    position: relative;
    width: 60%;
    height: 100%;
    background-color: #212731;

    .title_logo {
      flex: 0 0 63px;
      width: 63px;
      height: 39px;
      background: url("../assets/img/background/cloud_logo.svg") no-repeat center center;
      background-size: 100% 100%;
    }

    .count {
      display: flex;
      flex: 0 0 auto;
      align-items: center;
      margin-left: 37px;
      font-size: $fontSize;
      color: #fff;

      >span {
        display: flex;
        align-items: center;
        margin-left: 10px;

        i {
          display: inline-block;
          width: 27px;
          flex: 0 0 27px;
          height: 22px;
          background-size: 100% 100%;
          margin-right: 10px;
        }

        >span {
          color: #fff;
          flex: 0 0 50px;
        }

        &:nth-child(3) {
          i {
            width: 15px;
          }
        }
      }
    }

    .book_content {
      position: relative;
      height: calc(100% - 60px);
      width: 100%;

      .book_case {
        position: relative;
        height: 100%;
        // .swiper {
        //   width: 100%;
        //   height: 100%;
        // }
        // .swiper-slide {
        //   width: 83% !important;
        //   font-size: $fontSize;
        //   transition: 300ms;
        //   transform: scale(0.95);
        //   filter: blur(1px);
        // }
        // .swiper-slide-active,
        // .swiper-slide-duplicate-active {
        //   transform: scale(1);
        //   filter: none !important;
        // }
      }

      .swiper {
        width: 83%;
        height: 100%;
      }

      .swiper-slide {
        text-align: center;
        font-size: 18px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
      }
    }
  }
}

.progress {
  position: relative;
  display: flex;
  margin-left: 32px;
  margin-right: 20px;
  margin-top: 12px;
  flex: 0 0 400px;
  width: 400px;
  height: 8px;
  border-radius: 1px;
  background-color: #18a15e;

  .used {
    position: absolute;
    height: 8px;
    left: 0;
    top: 0;
    background: linear-gradient(0deg, #ff6c4f 0%, #d80f00 100%);
  }

  .remaining {
    position: absolute;
    right: 0;
    font-size: $fontSize;
    top: -24px;
    color: #7c7c7c;

    span {
      color: #fff;
    }
  }

  span {
    display: inline-block;
    flex: 0 0 50px;
    color: #fff;
  }
}


@-webkit-keyframes bounce-down {
  25% {
    -webkit-transform: translateY(-4px);
  }

  50%,
  100% {
    -webkit-transform: translateY(0);
  }

  75% {
    -webkit-transform: translateY(4px);
  }
}

@keyframes bounce-down {
  25% {
    transform: translateY(-4px);
  }

  50%,
  100% {
    transform: translateY(0);
  }

  75% {
    transform: translateY(4px);
  }
}

.add-shugui {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 31px;
  background: url('../assets/img/background/add-sg.png') no-repeat center center;
  background-size: 100% 100%;
  z-index: 99;
  cursor: pointer;
  // -webkit-animation: bounce-down 1.6s linear infinite;
  // animation: bounce-down 1.6s linear infinite;

  &:hover {
    background: url('../assets/img/background/add-sg-hover.png') no-repeat center center;
    background-size: 100% 100%;
  }
}

.pre-btn {
  transform: translateX(-50%) rotate(180deg) !important;
  width: 35px;
  height: 31px;
  background: url('../assets/img/background/curr-btn.png') no-repeat center center;
  background-size: 100% 100%;

  &:hover {
    background: url('../assets/img/background/curr-btn-hover.png') no-repeat center center;
    background-size: 100% 100%;
  }
}

.swiper-btn {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  font-size: 24px !important;
  z-index: 22;
  cursor: pointer;
}

.next-btn {
  bottom: 4px;
  width: 35px;
  height: 31px;
  background: url('../assets/img/background/curr-btn.png') no-repeat center center;
  background-size: 100% 100%;

  &:hover {
    background: url('../assets/img/background/curr-btn-hover.png') no-repeat center center;
    background-size: 100% 100%;
  }
}

.downBtn {
  position: absolute;
  bottom: 4px;
  display: flex;
  left: 50%;
  z-index: 5;
  transform: translateX(-50%);
}</style>