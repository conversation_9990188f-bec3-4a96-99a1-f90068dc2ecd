<template>
  <bookshelf :data="data" ref="bookshelfRef1"></bookshelf>
</template>
<script lang="ts" setup>
import Bookshelf from './Bookshelf';
import { defineProps, watch , defineExpose, ref, computed } from 'vue';
const { data } = defineProps({
  data: {
    type: Array
  }
});
let bookshelfRef1 = ref()

let exports = computed(() => bookshelfRef1)
defineExpose({
  exports
})
</script>
<style lang="scss" scoped></style>
