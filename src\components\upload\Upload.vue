<template>
  <input ref="inputRef" type="file" multiple name="file" id="file" @change="fileUpload" style="display:none">
</template>
<script setup lang="ts">
import { defineProps, computed, ref, defineExpose, defineEmits, h } from "vue";
import { ElMessage as message } from "element-plus";
import { Modal } from "ant-design-vue";
import indexApi from "@/api/Index";
import $ from "jquery";
import { useStore } from "vuex";
const store = useStore();
const { acceptList } = defineProps({
  acceptList: {
    type: Array,
  },
});
const inputRef = ref<any>(null);
const emit = defineEmits(["sendFileList"]);
let accept = computed(() => {
  let newArr = acceptList?.map((item) => {
    return "." + item;
  });
  return newArr?.join(",");
});
const selectFile = () => {
  $("#file").trigger("click");
};

// 获取账号信息
const findAccountsByOrgs = async () => {
  return false
  const { data, code, msg } = await indexApi.findAccountsByOrgs();
  if (!data.length) {
    let icon = h(
      "i",
      {
        style: {
          display: "inline-block",
          borderRadius: "50%",
          width: "20px",
          heigh: "20px",
          lineHeight: "20px",
          textAlign: "center",
          backgroundColor: "#fdb619",
          color: "#fff",
          fontSize: "16px !important",
          marginRight: "4px",
          flex: "0 0 20px",
          height: "20px",
        },
      },
      "!"
    );

    let icon1 = h("i", {
      style: {
        display: "inline-block",
        borderRadius: "50%",
        width: "12px",
        heigh: "12px",
        marginRight: "4px",
        flex: "0 0 12px",
        height: "12px",
      },
      class: "dialog_alert",
    });

    let protitle1 = h(
      "div",
      {
        class: "dti_pro_title dti_v_title",
        style: {
          fontSize: "14px",
          width: "440px",
        },
      },
      "试用版：单个文件大小不超过100M，容量1G"
    );

    let protitle2 = h(
      "div",
      {
        class: "dti_pro_title dti_v_title",
        style: {
          fontSize: "14px",
          width: "440px",
        },
      },
      "专业版：单个文件大小无限制，容量按需申请"
    );
    let title = h(
      "div",
      {
        class: "dti_title",
        style: {
          fontSize: "16px",
        },
      },
      "您的公司暂无服务云账号，请联系管理员注册"
    );
    let oneRow = h(
      "div",
      {
        class: "dti_alert",
        style: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          color: "#666",
        },
      },
      [icon1, "(管理员：负责付费账号的管理和扩容、新建书柜的审批)"]
    );
    let twoRow = h(
      "div",
      {
        style: {
          color: "#333",
          fontSize: "14px !important",
        },
      },
      "如果您的公司暂无管理员，可联系数研院咨询详情"
    );

    let threeRow = h(
      "div",
      {
        style: {
          color: "#333",
          fontSize: "14px !important",
        },
      },
      "客服工作时间：工作日，9:00:-17:30"
    );
    let fourRow = h(
      "div",
      {
        style: {
          color: "#333",
          fontSize: "14px !important",
        },
      },
      "客服热线：025-83676888"
    );

    let contactInformation = h(
      "div",
      {
        class: "contactInformation",
        style: {
          marginTop: "14px",
        },
      },
      [twoRow, threeRow, fourRow]
    );

    let contents = h(
      "div",
      {
        style: {
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        },
      },
      [title, oneRow, protitle1, protitle2, contactInformation]
    );

    Modal.confirm({
      closable: true,
      keyboard: false,
      width: 550,
      maskClosable: true,
      class: "alert confirm noAdminAlert",
      okText: () => "关闭",
      cancelText: () => "取消",
      title: () => "提示",
      content: () => contents,
      onOk() {},
    });
  } else {
    let protitle1 = h(
      "div",
      {
        class: "dti_pro_title",
        style: {
          fontSize: "14px",
        },
      },
      "体验版：单个文件大小不超过100M，容量1G"
    );

    let protitle2 = h(
      "div",
      {
        class: "dti_pro_title",
        style: {
          fontSize: "14px",
        },
      },
      "专业版：单个文件大小无限制，容量按需申请"
    );
    let title = h(
      "div",
      {
        style: {
          fontSize: "16px",
        },
      },
      "如需使用专业版请联系管理员"
    );

    let contents = h(
      "div",
      {
        style: {
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        },
      },
      [protitle1, protitle2, title]
    );

    Modal.confirm({
      closable: true,
      keyboard: false,
      width: 400,
      maskClosable: true,
      class: "alert confirm noAdminAlert",
      okText: () => "关闭",
      cancelText: () => "取消",
      title: () => "提示",
      content: () => contents,
      onOk() {},
    });
  }
};

const fileUpload = async () => {
  if (inputRef.value.files.length > 30) {
    message.warning("上传文件不能超过30个");
  } else {
    let uploadSpace = 0;

    for (let i = 0; i < inputRef.value.files.length; i++) {
      if (!store.state.bookCase.dataSpaceNum) {
        if (inputRef.value.files[i].size / 1024 / 1024 > 100) {
          inputRef.value.value = null;

          return findAccountsByOrgs();

          return message.warning("单个文件不能超过100M！");
        }
      }

      uploadSpace += inputRef.value.files[i].size;
    }
    const { data, code, msg } = await indexApi.checkSpace({
      uploadSpace,
      bookcaseId: store.state.bookCase.uuid,
    });
    if (!data) {
      emit("sendFileList", { files: inputRef.value.files, uploadSpace });
    } else {
      message.warning("网盘容量不足，请联系管理员购买容量!");
    }
  }
  inputRef.value.value = null;
};
defineExpose({ selectFile });
</script>