$blue: #3162a7;
$red: #e05e53;
$fontSize: 12px;
$gray: #999999;
.btn_bg,
.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $blue;
  line-height: 28px;
  height: 28px;
  font-size: $fontSize;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;
  margin-right: 10px;
  &:last-child {
    margin-right: 0;
  }
}

.btn {
  border: 1px solid $blue;
  background-color: transparent;
  color: $blue;
}

.red_style {
  color: $red;
  border-color: $red;
}

.el-breadcrumb__item {
  .el-breadcrumb__inner {
    color: $blue !important;
    cursor: pointer;
  }
  &:last-child {
    .el-breadcrumb__inner {
      color: $gray !important;
      cursor: pointer;
    }
  }
}
.el-rate {
  .el-rate__item {
    &:last-child {
      i {
        margin-right: 0 !important;
      }
    }
  }
}

.tip_content {
  div {
    line-height: 20px;
    font-size: $fontSize;
    color: #333333;
    width: 166px;
    .label {
      color: $gray;
    }
  }
}

.empty {
  position: relative;
  width: 40%;
  height: 100%;
  background-color: #f0f2f5;
  div {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    top: 400px;
    left: 50%;
    transform: translateX(-50%);
    span {
      margin-top: 21px;
      color: $gray;
      font-size: $fontSize;
    }
  }
}

.atooltip {
  background-color: #0375de !important;
  padding: 3px !important;
}

.atooltip.el-popper.is-light .el-popper__arrow::before {
  background: #0375de !important;
  border: none;
}
.atooltip.el-popper.is-light .el-popper__arrow::before {
  background: #0375de !important;
  border: none;
}

.ant-popover-inner-content {
  padding: 0 !important;
}
.ant-modal-confirm-title {
  height: 36px !important;
  line-height: 36px !important;
  font-size: $fontSize !important;
  font-weight: 700 !important;
  color: #fff !important;
  background-color: $blue;
  padding: 0 16px;
}
.alert .ant-modal-close-x {
  width: 36px !important;
  height: 36px !important;
}
.ant-modal-confirm-content {
  font-size: $fontSize !important;
  text-align: center;
  line-height: 30px;
  padding: 25px;
  margin-left: 0 !important;
}
.ant-modal-confirm .ant-modal-confirm-btns {
  display: flex;
  justify-content: center;
  float: none;
  margin-top: 0 !important;
  margin-bottom: 35px;
}
.alert {
  .ant-btn {
    height: 32px !important;
    line-height: 27px !important;
    border-radius: 3px;
    border: none !important;
    background-color: #c1c1c1 !important;
    span {
      color: #fff;
    }
  }
  .ant-btn-primary {
    line-height: 26px !important;
    padding: 4px 15px !important;
    height: 32px !important;
    background: $blue !important;
    border-radius: 3px;
    border: none !important;
  }
}

.left_bg {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 90px;
  height: 90%;
  background: url('../img/background/left_color_bg_1.png') no-repeat center center;
  background-size: 100% 100%;
}
.has_left_bg {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 90px;
  height: 90%;
  background: url('../img/background/left_color_bg.png') no-repeat center center;
  background-size: 100% 100%;
}

.right_bg {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 90px;
  height: 90%;
  background: url('../img/background/right_color_bg_1.png') no-repeat center center;
  background-size: 100% 100%;
}
.has_right_bg {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 90px;
  height: 90%;
  background: url('../img/background/right_color_bg.png') no-repeat center center;
  background-size: 100% 100%;
}
.ant-message {
  top: 30% !important;
}
.table_body {
  .el-rate {
    --el-rate-icon-margin: 0 !important;
  }
}
.ant-progress-outer {
  position: relative;
}
.ant-progress-text {
  position: absolute;
  left: 0;
  color: #fff !important;
  font-size: $fontSize;
  margin-left: 0;
}
.ant-progress {
  position: relative;
}
.ant-progress-show-info .ant-progress-outer {
  padding: 0 !important;
}
.ant-progress-inner {
  margin-top: 16px;
}
.ico_docu {
  background: url('../../assets/img/icon/user_dept.png') no-repeat center center !important;
}

.ant-pagination-item-active,
.ant-pagination-item:focus,
.ant-pagination-item:hover,
.ant-pagination-prev:focus .ant-pagination-item-link,
.ant-pagination-next:focus .ant-pagination-item-link,
.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  border: 1px solid #3161a9 !important;
}

.ant-pagination-item-active a,
.ant-pagination-item:focus a,
.ant-pagination-item:hover a,
.ant-pagination-prev:focus .ant-pagination-item-link,
.ant-pagination-next:focus .ant-pagination-item-link,
.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  color: #3161a9 !important;
}

.pagination .ant-pagination-item-active,
.pagination .ant-pagination-item:focus,
.pagination .ant-pagination-item:hover,
.pagination .ant-pagination-prev:focus .ant-pagination-item-link,
.pagination .ant-pagination-next:focus .ant-pagination-item-link,
.pagination .ant-pagination-prev:hover .ant-pagination-item-link,
.pagination .ant-pagination-next:hover .ant-pagination-item-link {
  background-color: #3161a9 !important;
  color: #fff !important;
  border: 1px solid #3161a9 !important;
}

.pagination .ant-pagination-item-active a,
.pagination .ant-pagination-item:focus a,
.pagination .ant-pagination-item:hover a,
.pagination .ant-pagination-prev:focus .ant-pagination-item-link,
.pagination .ant-pagination-next:focus .ant-pagination-item-link,
.pagination .ant-pagination-prev:hover .ant-pagination-item-link,
.pagination .ant-pagination-next:hover .ant-pagination-item-link {
  color: #fff !important;
}

.pagination .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon,
.pagination .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon {
  color: #3161a9 !important;
}

// .pagination .ant-pagination-options-quick-jumper input:focus,
// .pagination .ant-pagination-options-quick-jumper input:hover {
//   border-color: rgb(34, 172, 56) !important;
//   box-shadow: 0 0 0 2px rgb(34, 172, 56 / 20%);
// }
.el-icon svg {
  font-size: 16px !important;
}
.el-form--label-top .el-form-item__label {
  padding: 0 !important;
  line-height: 0 !important;
}
.fileInfoTip {
  .el-popper__arrow {
    transform: translateX(20px) !important;
  }
}

.hide_plus {
  svg {
    display: none !important;
  }
}

.el-message {
  top: 40% !important;
  min-width: 90px !important;
}

.disabled {
  border: 1px solid #999;
  color: #999 !important;
  cursor: not-allowed !important;
  background-color: #fff !important;
  .el-rate__icon {
    color: #999999 !important;
  }
}

.tranformStyle {
  display: inline-block !important;
  transform: scale(0.7) !important;
  font-weight: bold;
}
