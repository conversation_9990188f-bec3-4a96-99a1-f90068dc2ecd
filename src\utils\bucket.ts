import { Config, QingStor } from 'qingstor-sdk';

const config = new Config({
  access_key_id: 'RSKBCWLOYAZKGDRBDGQE',
  secret_access_key: '8cwqzDGFz2nuH7ZVgiESebJWNWPN78H7zYwxYd6L',
});


const bucket = new QingStor(config).Bucket('jchcxxzx', 'sh1a');

export const bucketList = () => {
  bucket
    .listObjects({
      limit: 10,
    })
    .then((response: any) => {
      console.log(response.data);
    })
    .catch((error: any) => {
      console.log(error.response.data);
    });
};
