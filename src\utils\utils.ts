export default class utils {
  static getQueryUrl(key: any) {
    const reg = new RegExp('(^|&)' + key + '=([^&]*)(&|$)');
    const r = window.location.search.substr(1).match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    return '';
  }
  static rewrite(url: any) {
    if (process.env.NODE_ENV === 'development') {
      return url + (url.indexOf('?') === -1 ? '?' : '&') + 'wx_user_id=' + utils.getQueryUrl('wx_user_id');
    } else {
      const userId = utils.getQueryUrl('wx_user_id');
      let _url: string = process.env.BASE_URL;
      if (_url[_url.length - 1] === '/') {
        _url = _url.substring(0, _url.length - 1);
      }
      if (userId) {
        return _url + url + (url.indexOf('?') === -1 ? '?' : '&') + 'wx_user_id=' + userId;
      } else {
        return _url + url;
      }
    }
  }
  static uuid() {
    var s:any = [];
    var hexDigits = '0123456789abcdef';
    for (var i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the
    // clock_seq_hi_and_reserved
    // to 01
    s[8] = s[13] = s[18] = s[23] = '';
    var uuid = s.join('');
    return uuid;
  }
}
