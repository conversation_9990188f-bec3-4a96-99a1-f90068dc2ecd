<template>
  <div class="table_header">
    <table :style="{ background: !table.length ? '#fff' : '' }">
      <colgroup>
        <col />
        <col width="70px" />
        <col width="100px" />
        <col width="100px" />
        <col width="100px" />
      </colgroup>
      <tr>
        <th>
          <div class="cell" style="justify-content: flex-start; margin-left: 39px">
            <el-checkbox style="margin-right: 20px" v-model="checkAll" @change="
                (val) => {
                  handleSelect(0, val);
                }
              "></el-checkbox>
            文件名
            <span class="caret-wrapper">
              <i class="sort-caret ascending" @click="listSort({ type: 'fileNameSort', sort: 'ASC' })" :class="
                  upParams?.fileNameSort == 'ASC' ? 'ascending_active' : ''
                "></i>
              <i class="sort-caret descending" @click="listSort({ type: 'fileNameSort', sort: 'DESC' })" :class="
                  upParams?.fileNameSort == 'DESC' ? 'descending_active' : ''
                "></i>
            </span>
          </div>
        </th>
        <th>
          <div class="cell">
            大小
            <span class="caret-wrapper">
              <i class="sort-caret ascending" @click="listSort({ type: 'totalSpaceSort', sort: 'ASC' })" :class="
                  upParams?.totalSpaceSort == 'ASC' ? 'ascending_active' : ''
                "></i>
              <i class="sort-caret descending" @click="listSort({ type: 'totalSpaceSort', sort: 'DESC' })" :class="
                  upParams?.totalSpaceSort == 'DESC' ? 'descending_active' : ''
                "></i>
            </span>
          </div>
        </th>
        <th>
          <div class="cell">
            删除时间
            <span class="caret-wrapper">
              <i class="sort-caret ascending" @click="listSort({ type: 'dataCreateTimeSort', sort: 'ASC' })" :class="
                  upParams?.dataCreateTimeSort == 'ASC'
                    ? 'ascending_active'
                    : ''
                "></i>
              <i class="sort-caret descending" @click="listSort({ type: 'dataCreateTimeSort', sort: 'DESC' })" :class="
                  upParams?.dataCreateTimeSort == 'DESC'
                    ? 'descending_active'
                    : ''
                "></i>
            </span>
          </div>
        </th>
        <th>删除人</th>
        <th>有效期</th>
      </tr>
    </table>
  </div>
  <div class="table_body" :style="{ height: maxHeight + 'px' }">
    <div class="empty" style="width: 100%" v-if="!table.length">
      <div>
        <img src="../../assets/img/background/fload.png" alt="" />
        <span>您还没删除任何文件</span>
      </div>
    </div>
    <table class="title" v-else>
      <colgroup>
        <col />
        <col width="70px" />
        <col width="100px" />
        <col width="100px" />
        <col width="100px" />
      </colgroup>
      <template v-for="(item, index) in table" :key="item.uuid">
        <tr 
          :class="item.check && item.canEdit ? 'active_bg' : 'no_bg'" 
          :style="{ backgroundColor: item.canEdit ? '#E5F7FF' : '' }" 
          v-if="item.show" :data-key="item.rowKey" 
          @mouseenter="showClassSetting(item, $event)">
          <td>
            <div class="td_cell">
              <span :style="{ paddingLeft: item.leave * 15 + 'px' }"></span>
              <i :style="{
                  visibility:
                    item.type === 'folder' && !item.isAdd ? 'visible' : 'hidden'
                }" @click="showTR(item)" :class="item.unfold ? 'icon_hide' : 'icon_show'"></i>
              <el-checkbox v-if="item.dataDelFlag === 1 || item.type === 'file'" @change="handleSelect(1, item)" :style="{
                  visibility: (item.canEdit && item.dataDelFlag===1) && !item.isAdd ? 'visible' : 'hidden'
                }" v-model="item.check"></el-checkbox>
              <!-- 文件夹图标 -->
              <span class="icon_common" v-if="item.type === 'folder'"></span>
              <span class="icon_file" v-if="item.type !== 'folder'" :style="{ backgroundImage: `url(${getIcon(item.showType)}` }"></span>
              <!-- 文件名  -->
              <el-tooltip popper-class="fileInfoTip" :placement="item.isBottom ? 'bottom-start' : 'top-start'" effect="light" v-if="item.type === 'file' && !item.isEdit">
                <template #content>
                  <div class="tip_content">
                    <div>{{ getFileName(item) }}</div>
                    <div>
                      <span class="label">文件类型：{{ item.showType }}</span>
                    </div>
                    <div>
                      <span class="label">所属文件夹：{{ selectFolder.fileName }}</span>
                    </div>
                    <div>
                      <span class="label">关键词：{{ item.keyword }}</span>
                    </div>
                  </div>
                </template>
                <span class="fileName">{{  getFileName(item) }}</span>
              </el-tooltip>
              <!-- 文件夹名 -->
              <span class="fileName" :class="{ notDelete: item.dataDelFlag !== 1 }" v-if="item.type === 'folder' && !item.isAdd && !item.isEdit">{{ item.fileName }}</span>
            </div>
          </td>
          <td>
            <span class="size" v-if="item.type === 'file' && item.canEdit">
              <el-tooltip v-if="item.type === 'file'" :content="renderSize(item.totalSpace,1)" placement="bottom" effect="light">
                <span>{{ renderSize(item.totalSpace,0) }}</span>
              </el-tooltip>
              <a-tooltip v-if="!isRecycle" :placement="item.isBottom ? 'bottom' : 'top'" color="#fff" :getPopupContainer="
                  (e) => {
                    return e;
                  }
                ">
                <template #title>
                  <ul class="more">
                    <li @click="downLoad(item)">下载</li>
                    <li @click="remove(item)">删除</li>
                    <li @click="move(item)">移动</li>
                    <li @click="openNewLabel(item)">标签</li>
                  </ul>
                </template>
                <div class="more_icon">
                  <img src="../../assets/img/icon/dot.png" />
                </div>
              </a-tooltip>
            </span>
          </td>
          <td>
            <el-tooltip v-if="item.dataDelFlag == 1" :content="item.dataUpdateTime" :placement="item.isBottom ? 'bottom' : 'top'" effect="light">
              <span>{{
                getDateDiff(dayjs(item.dataUpdateTime).format('YYYY-MM-DD'))
              }}</span>
            </el-tooltip>
          </td>
          <td>
            <el-tooltip v-if="item.dataDelFlag == 1" :content="item.dataUpdateUserName" :placement="item.isBottom ? 'bottom' : 'top'" effect="light">
              <span class="inline_type" v-html="item.dataUpdateUserName"></span>
            </el-tooltip>
          </td>
          <td>{{ item.dataDelFlag === 1 ? item.vaildDate + '天' : '' }}</td>
        </tr>
      </template>
    </table>
  </div>
  <newLabel v-if="newLabelParams.visible" :params="newLabelParams" @close="closeNewLabel"></newLabel>
  <moveLog v-if="moveParams.visible" :params="moveParams" @close="closeMoveLog"></moveLog>
  <SettingUser :type="5" v-if="settingUserParams.visible" :params="settingUserParams" @close="closeSettingUser"></SettingUser>
</template>
<script lang="ts" setup>
import {
  ref,
  defineProps,
  watch,
  nextTick,
  defineEmits,
  reactive,
  defineExpose,
  computed,
  inject,
} from "vue";
import {  Modal } from "ant-design-vue";
import { ElMessage as message } from "element-plus";
import commonHooks from "@/hooks/commonHooks";
import moveLog from "../alert/move.vue";
import newLabel from "../alert/newLabel.vue";
import indexApi from "@/api/Index";
import SettingUser from "../rightsProfile/SettingUser.vue";
import { useStore } from "vuex";
import dayjs from "dayjs";
const store = useStore();
const { getDateDiff, getIcon, renderSize } = commonHooks();
const props = defineProps({
  tableData: {
    type: Array,
    default: [],
  },
  maxHeight: {
    type: Number,
  },
  searchParams: {
    type: Object,
  },
  editDataList: {
    type: Function,
  },
  editLevel: {
    type: Function,
  },
  isRecycle: {
    type: Boolean,
    default: false,
  },
});
let upParams = ref();
// 修改文件名
const getFileName = (file: any) => {
  if (file.fileName.endsWith(`.${file.showType}`)) {
    return file.fileName.substring(0, file.fileName.lastIndexOf(`.${file.showType}`))
  } else {
    return file.fileName
  }
}
watch(
  () => props.searchParams,
  (newVal, oldVal) => {
    upParams.value = JSON.parse(JSON.stringify(newVal));
  },
  {
    deep: true,
    immediate: true,
  }
);
// 配置人员参数
let settingUserParams = reactive({
  visible: false,
  title: "权限配置",
  alert: "请配置文件的查看权限",
  // 书本id
  uuid: "",
  // 选中文件
  filterCheck: [],
});
let checkAll = ref(false);
const settingUser = (val) => {
  if (!val.canEdit) return;
  if (props.isRecycle) {
    return;
  }
  settingUserParams.filterCheck = [val];
  settingUserParams.uuid = selectFolder.value.uuid;
  settingUserParams.visible = true;
};
const closeSettingUser = () => {
  settingUserParams.visible = false;
};
const emit = defineEmits(["remove", "sort"]);
// 左侧书本选中
const selectFolder = computed(() => store.state.selectFolder);
// 右侧文件夹选中
const rightSelectFolder = computed(() => store.state.rightSelectFolder);
let clientHeight = ref(0);
let newFileName = ref("");
nextTick(() => {
  clientHeight.value = document.body.clientHeight;
});
// 新数据
let table: any = ref([]);
// 备份新数据
let copyTable: any = ref([]);
// 递归原数据将所有子节点全部放在一级
const deep = (val: any, parentId: string, level = 1, parentIndex: number) => {
  val.forEach((item: any, index: number) => {
    // 是有否此节点
    let oldRow = copyTable.value.find((items: any) => {
      return items.uuid == item.uuid;
    });
    // 节点显示/隐藏
    if (parentId) {
      item.parentId = parentId;
      if (oldRow && typeof oldRow.show == "boolean") {
        item.show = oldRow.show;
      } else {
        item.show = true;
      }
    } else {
      item.show = true;
    }
    // 节点层级
    item.leave = level;
    // 选中状态
    // item.check = oldRow ? oldRow.check : false;
    item.check = false;
    // 节点标识
    item.rowKey =
      (parentIndex ? parentIndex + "_" : "") +
      (index + 1 < 10 ? "0" : "") +
      (index + 1);
    // 子节点关闭/展开
    item.unfold =
      typeof item.unfold == "boolean"
        ? item.unfold
        : oldRow
        ? oldRow.unfold
        : false;

    // 是否编辑
    item.edit = false;
    table.value.push(item);
    if (item.children && item.children.length) {
      item.isParent = true;
      deep(item.children, item.uuid, level + 1, item.rowKey);
    } else {
      item.isParent = false;
    }
  });
};
const resetOld = () => {
  table.value = [];
  copyTable.value = [];
};
const initTable = (newVal) => {
  let tableList = JSON.parse(JSON.stringify(newVal));
  copyTable.value = JSON.parse(JSON.stringify(table.value));
  table.value = [];
  checkAll.value = false;
  deep(tableList);
};
// 展开/关闭子节点
const showTR = (val: any) => {
  val.unfold = !val.unfold;
  console.log(val)
  if (val.unfold) {
    store.commit("setAsyncFolder", val);
    table.value.forEach((item: any) => {
      if (item.parentId && item.parentId == val.uuid) {
        item.show = val.unfold;
        console.log(item)
      }
    });
    copyTable.value = JSON.parse(JSON.stringify(table.value));
    props.editDataList(val, 2);

  } else {
    table.value.forEach((item: any) => {
      if (
        item.rowKey.indexOf(val.rowKey) ===0 &&
        item.rowKey.length > val.rowKey.length
      ) {
        console.log(item,'unfoldfalse')
        item.unfold = false;
        item.show = false;
      }
    });
  }
};
// 全选/反选
const handleSelect = (type, val: any) => {
  if (!type) {
    table.value.forEach((item) => {
      if (item.canEdit) {
        item.check = val;
      }
    });
  }
  let selectArr = table.value.filter((item) => {
    if (item.canEdit) {
      return item.check == true;
    }
  });

  if (
    selectArr.length ==
      table.value.filter((item) => {
        return (item.canEdit);
      }).length &&
    table.value.filter((item) => {
      return (item.canEdit);
    }).length
  ) {
    checkAll.value = true;
  } else {
    checkAll.value = false;
  }
  store.commit("setRightSelectFolder", {});
  if (val.type != "file") {
    // 选择文件夹则取消子文件夹包括同级父文件夹选中
    if (val.check) {
      table.value.forEach((item: any) => {
        if (
          (item.rowKey.indexOf(val.rowKey) == 0 ||
            val.rowKey.indexOf(item.rowKey) === 0) &&
          item.rowKey.length != val.rowKey.length
        ) {
          item.check = false;
        }
      });
    }
  }
  props.editLevel();

  // if (val.type != "file") {
  //   store.commit("setRightSelectFolder", val);
  //   // 将所有该节点下的子节点全选
  //   table.value.forEach((item: any) => {
  //     if (item.rowKey.indexOf(val.rowKey) == 0) {
  //       item.check = val.check;
  //     }
  //   });
  //   const deepData = (val) => {
  //     //   父节点
  //     let parent = table.value.find((item: any) => {
  //       return item.uuid == val.parentId;
  //     });
  //     if (parent) {
  //       //   所有的兄弟节点
  //       let arr = table.value.filter((item: any) => {
  //         if (item.parentId == val.parentId) {
  //           return item;
  //         }
  //       });
  //       // 未选中的兄弟节点
  //       let index = arr.findIndex((item: any) => {
  //         return !item.check;
  //       });
  //       if (index == -1) {
  //         parent.check = true;
  //         deepData(parent);
  //       } else {
  //         parent.check = false;
  //         table.value.forEach((item: any) => {
  //           if (
  //             val.rowKey.indexOf(item.rowKey) == 0 &&
  //             val.rowKey.length != item.rowKey.length
  //           ) {
  //             item.check = false;
  //           }
  //         });
  //       }
  //     }
  //   };
  //   deepData(val);
  // }
};
// 文件详细操作展示位置
const showClassSetting = (val: any, e: any) => {
  let height = clientHeight.value - $(e.target).offset().top;
  height < 220 ? (val.isBottom = false) : (val.isBottom = true);
};
// 删除文件
const remove = (val: any) => {
  Modal.confirm({
    closable: true,
    keyboard: false,
    width: 413,
    class: "alert",
    okText: () => "确定",
    cancelText: () => "取消",
    title: () => "确定删除",
    content: () => "确定删除该文件吗?删除后在30天内可以通过回收站还原",
    onOk() {
      // val.check = true;
      // handleSelect(val);
      indexApi.fileFolderDelete({ uuid: val.uuid }).then((res) => {
        if (res.code == 200) {
          emit("remove", val);
        } else {
          message.warning(res.msg);
        }
      });
    },
    onCancel() {},
  });
};
// 向外暴露方法
defineExpose({ table, handleSelect, initTable, resetOld });
// 移动弹框
const moveParams = reactive({
  // 选中文件
  filterCheck: [],
  visible: false,
});
const initRightTable = inject("initRightTable");
const closeMoveLog = (val: any) => {
  resetOld();
  store.commit("setClear");
  initRightTable();
  moveParams.visible = false;
};
const move = (val: any) => {
  moveParams.filterCheck[0] = val;
  moveParams.visible = true;
};
// 标签弹框数据
const newLabelParams = reactive({
  visible: false,
  uuid: "",
  level: "",
  fileName: "",
  bookcaseId: "",
  type: "",
  parentId: "",
  totalSpace: "",
  linkedUser: [],
  canEdit: true,
});
const openNewLabel = (item) => {
  newLabelParams.uuid = item.uuid;
  newLabelParams.level = item.level;
  newLabelParams.fileName = item.fileName;
  newLabelParams.bookcaseId = item.bookcaseId;
  newLabelParams.parentId = item.parentId;
  newLabelParams.type = item.type;
  newLabelParams.linkedUser = item.linkedUser;
  newLabelParams.totalSpace = item.totalSpace;
  newLabelParams.visible = true;
};
const closeNewLabel = () => {
  newLabelParams.visible = false;
};
// 数据排序
const listSort = (val: any) => {
  resetOld();
  emit("sort", val);
};
const clearAdd = inject("clearAdd");
// 清空文件夹名称
const clearFileName = () => {
  newFileName.value = "";
};

// 下载单个
const downLoad = async (val) => {
  window.location.href = "/jchc-edisk/fileFolder/download/" + val.uuid;
};
</script>

<style lang="scss" scoped>
$fontSize: 12px;
.fileName {
  display: inline-block;
  flex: 0 0 68%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  cursor: pointer;
}
.notDelete.fileName {
  color: #ccc;
}
.table_header {
  min-width: 100%;
  table {
    width: 100%;
  }
  tr {
    border-top: 1px solid #e0e8f2;
  }
}
.table_body {
  min-width: 100%;
  overflow: auto;
  table {
    width: 100%;
    .no_bg {
      &:hover {
        border-left: 1px solid #107df8;
        border-right: 1px solid #107df8;
        td {
          border-bottom: 1px solid #107df8;
          border-top: 1px solid #107df8;
        }
      }
    }
  }
}
table {
  table-layout: fixed;
  cursor: default;
  tr {
    height: 32px;
    border: 1px solid #e0e8f2;
    &:hover {
      .size {
        .more_icon {
          display: inline-block;
        }
      }
    }
    td,
    th {
      font-size: $fontSize;
      text-align: center;
    }
  }
}

.table_body i {
  display: inline-block;
  flex: 0 0 14px;
  height: 14px;
  background: url("../../assets/img/icon/table_hide.png") no-repeat center
    center;
  background-size: 100% 100%;
}

.td_cell {
  display: flex;
  align-items: center;
  i {
    margin-right: 10px;
  }
}
.el-checkbox {
  margin-right: 10px;
}
.inline_type {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
.more {
  width: 60px;
  li {
    font-size: $fontSize;
    line-height: 28px;
    text-align: center;
    cursor: pointer;
    &:hover {
      color: #3162a7;
    }
  }
}
.size {
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    margin-right: 10px;
  }
  .more_icon {
    height: 30px;
    line-height: 28px;
    display: none;
  }
}
.empty {
  div {
    top: 50% !important;
    transform: translate(-50%, -50%);
  }
}
.table_header {
  .cell {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    .caret-wrapper {
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      height: 20px;
      width: 24px;
      vertical-align: middle;
      cursor: pointer;
      overflow: initial;
      position: relative;
    }
    .sort-caret {
      width: 0;
      height: 0;
      border: solid 5px transparent;
      position: absolute;
      left: 7px;
    }
    .ascending {
      border-bottom-color: #999;
    }
    .ascending_active {
      border-bottom-color: #3162a7 !important;
    }
    .descending {
      bottom: -3px;
      border-top-color: #999;
    }
    .descending_active {
      border-top-color: #3162a7 !important;
    }
  }
}
.icon_hide {
  background: url("../../assets/img/icon/table_hide.png") no-repeat center
    center !important;
}
.icon_show {
  background: url("../../assets/img/icon/table_show.png") no-repeat center
    center !important;
}
.icon_common {
  display: inline-block;
  flex: 0 0 19px;
  height: 15px;
  margin: 0 10px;
  background: url("../../assets/img/icon/folder_table_icon.png") no-repeat
    center center;
}
.icon_file {
  display: inline-block;
  flex: 0 0 19px;
  height: 15px;
  margin: 0 10px;
  background: url("../../assets/img/icon/other_table_icon.png") no-repeat center
    center;
  background-size: 75% 100%;
}
.delete_icon {
  display: inline-block;
  width: 13px;
  height: 13px;
  background: url("../../assets/img/icon/delete_table_icon.png") no-repeat
    center center;
  cursor: pointer;
  flex: 0 0 13px;
}
.submit {
  display: inline-block;
  width: 22px;
  height: 22px;
  background: url("../../assets/img/icon/submit_icon.png") no-repeat center
    center;
  cursor: pointer;
  margin-left: 10px;
}
.el-checkbox {
  height: 32px !important;
}
</style>
<style lang="scss">
.table_body .el-checkbox__inner {
  width: 15px !important;
  height: 15px !important;
}
.font {
  font-weight: bold !important;
}
.active_bg {
  background-color: #3162a7 !important;
  span {
    color: #fff !important;
  }
}
.active_bg td{
    color: #fff !important;
}
</style>
