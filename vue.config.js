const CompressionWebpackPlugin = require("compression-webpack-plugin")
// const UglifyJsPlugin = require('uglifyjs-webpack-plugin')
const isProduction = process.env.NODE_ENV === 'production';
module.exports = {
  assetsDir: 'resources',
  // publicPath: '/jchc-edisk',
  publicPath: './',
  lintOnSave: true,
  productionSourceMap: false,
  configureWebpack: {
    plugins: [
      // new webpack.ProvidePlugin({
      //     jQuery: 'jquery',
      //     $: 'jquery',
      //     "windows.jQuery": 'jquery'
      // })
      // new UglifyJsPlugin({
      //   uglifyOptions: {
      //     output: {
      //       comments: false, // 去掉注释
      //     },
      //     warnings: false,
      //     compress: {
      //       drop_console: true,
      //       drop_debugger: false,
      //       pure_funcs: ['console.log'] //移除console
      //     }
      //   }
      // }),
      new CompressionWebpackPlugin({
        test: /\.js$|\.html$|.\css/, //匹配文件名
        threshold: 10240, //对超过10k的数据压缩
        deleteOriginalAssets: false //不删除源文件
      })
    ]
  },
  configureWebpack: config => {
    if (isProduction) {
      // 用cdn方式引入
      return {
        plugins: [new CompressionWebpackPlugin({
          test: /\.(js|css)(\?.*)?$/i,
          threshold: 10240,
          deleteOriginalAssets: false
        })]
      };
    }

  },
  devServer: {
    proxy: 'http://192.168.112.211:8083/jchc-edisk' //江涛
    // proxy: 'http://192.169.245.60:80/jchc-edisk' //王涛
    // proxy: 'http://192.168.112.43:8080/jchc-edisk' //信成
  }
};