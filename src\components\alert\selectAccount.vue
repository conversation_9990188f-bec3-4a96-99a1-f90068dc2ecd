<template>
  <a-modal class="selectAccount" width="500px" :footer="null" v-model:visible="params.visible" :title="!params.newBuild ? '申请扩容':'申请新建书柜'" @cancel="close" :maskClosable="false">
    <div class="content newLabel">
      <div class="account">
        <template v-if="!params.isAccount">
          <a-steps :current="current">
            <a-step v-for="item in steps" :key="item.title" :title="item.title" />
          </a-steps>
        </template>
        <div class="steps-content" :class="params.isAccount ? 'no_style':''">
          <template v-if="!current">
          <!-- 您的公司有{{params.accountList.length}}个服务云账号， -->
            <div class="label">请选择服务云付费账号</div>
            <a-radio-group v-model:value="addParams.accountId">
              <a-radio :key="item.fVcUserCode" v-for="(item,index) in params.accountList" :style="radioStyle" :value="item.fPkRelationId">{{item.fVcOrgName+'(管理员-'+item.fVcUserName+')'}}</a-radio>
            </a-radio-group>
          </template>
          <template v-else-if="current==1">
            <a-form :model="addParams" name="basic" :wrapper-col="{ span: 16 }">
              <a-form-item label="选择容量">
                <a-select v-model:value="addParams.dataApplyNum" style="width:70px">
                  <a-select-option :value="index+1" v-for="(item,index) in 5">{{index+1}}T</a-select-option>
                </a-select>
              </a-form-item>
              <!--  <a-form-item label="申请数量"  v-else>
                <a-select v-model:value="formState.select" style="width:70px">
                  <a-select-option v-for="(item,index) in 10" :key="index" :value="item">{{item}}</a-select-option>
                </a-select>
              </a-form-item> -->
              <a-form-item label="申请原因">
                <a-textarea :maxlength="200" v-model:value="addParams.applyReason"></a-textarea>
              </a-form-item>
            </a-form>
          </template>
          <template v-else>
            <a-result class="alert result" status="success" :title="'您的申请已提交，请等待管理员：'+addParams.accountUserName+'('+addParams.accountUserCode+')审核!'">
              <template #extra>
                <div class="ant-btn-primary submit" @click="close">确定</div>
              </template>
            </a-result>
          </template>
        </div>
      </div>
      <div class="footer alert" v-if="current!=2">
        <div class="ant-btn-primary" @click="handlePrev">{{!current || params.isAccount?'取消' :'上一步'}} </div>
        <div class="ant-btn-primary" :class="(!current&&!addParams.accountId) || (current==1 && !addParams.applyReason) ? 'disabled':''" @click="handleNext">{{current==1 ? '提交' :'下一步'}} </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import {
  defineProps,
  ref,
  defineEmits,
  computed,
  onMounted,
  reactive,
} from "vue";
import { useStore } from "vuex";
import utils from "@/utils/utils";
import indexApi from "@/api/Index";
import { ElMessage as message } from "element-plus";
const { params, fileList } = defineProps({
  params: {
    type: Object,
  },
  fileList: {
    type: Array,
  },
});

const store = useStore();

const emit = defineEmits(["close"]);

const radioStyle = reactive({
  display: "flex",
  height: "30px",
  lineHeight: "30px",
});

const steps = ref([
  {
    title: "选择账号",
    id: 0,
  },
  {
    title: "发起申请",
    id: 1,
  },
  {
    title: "提交审核",
    id: 2,
  },
]);
// 节点
let current = ref(0);

// 扩容/新增入参
const addParams = reactive({
  accountId: "",
  accountOrgId: "",
  accountOrgName: "",
  accountUserCode: "",
  accountUserName: "",
  applyReason: "",
  bookcaseId: params.bookcaseId,
  applyType: !params.newBuild ? "expansion" : "newly",
  dataApplyNum: 1,
});

// 当前书柜有关联账号则直接跳到第二步
if (params.isAccount) {
  current.value = 1;
  // selectValue.value = 1;
  const {
    accountId,
    accountOrgId,
    accountOrgName,
    accountUserCode,
    accountUserName,
  } = params.accountList[0];
  params.accountList[0].fPkRelationId = accountId;
  params.accountList[0].fVcOrgCode = accountOrgId;
  params.accountList[0].fVcUserCode = accountUserCode;
  params.accountList[0].fVcUserName = accountUserName;
  params.accountList[0].fPkRelationId = accountId;
  params.accountList[0].fVcOrgName = accountOrgName;
  addParams.accountId = accountId;
}

const close = (val) => {
  emit("close");
};

const handlePrev = () => {
  if (!current.value || params.isAccount) {
    close();
  } else {
    current.value--;
  }
};

const handleNext = async () => {
  if (!addParams.accountId) return;
  if (current.value == 1) {
    if (!addParams.applyReason.trim()) {
      return message.warning("请填写申请原因!");
    }
    let find = params.accountList.find((item) => {
      return item.fPkRelationId == addParams.accountId;
    });
    addParams.accountOrgId = find.fVcOrgCode;
    addParams.accountOrgName = find.fVcOrgName;
    addParams.accountUserCode = find.fVcUserCode;
    addParams.accountUserName = find.fVcUserName;
    addParams.accountId = addParams.accountId + "";
    // params.newBuild ? (addParams.dataApplyNum = 1) : "";
    const { data, code, msg } = await indexApi[
      !params.newBuild ? "applyExpansion" : "applyNewly"
    ](addParams);
    if (code == 200) {
      current.value++;
    } else {
      message.warning(msg);
    }
  } else {
    current.value++;
  }
};
</script>
<style lang="scss" scoped>
$fontSize: 12px;
.ant-result {
  padding: 0 !important;
}
.newLabel {
  height: auto !important;
  padding: 10px 19px 10px 19px;
  .save {
    height: 30px;
    line-height: 30px;
    flex: 0 0 50px;
    text-align: center;
    background-color: #3162a7;
    color: #fff;
    border-radius: 4px;
    margin-left: 4px;
    cursor: pointer;
  }
  .title {
    font-size: $fontSize;
  }
  .el-input {
    margin-top: 0 !important;
  }
  .footer {
    display: flex;
    margin-top: 15px !important;
    justify-content: flex-end !important;
    .ant-btn {
      color: #fff;
      margin-right: 10px;
    }
    .ant-btn-primary {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .account {
    // background-color: #fff;
    min-height: 20px;
    padding: 10px 20px;
    border-radius: 5px;
  }
}
.steps-content {
  margin-top: 20px;
  border-top: 1px solid #797979;
  padding-top: 20px;
  height: 230px;
  overflow: auto;
  .result {
    ::v-deep .anticon {
      display: block !important;
      svg {
        font-size: 40px !important;
      }
    }
  }
  .label {
    margin-bottom: 20px;
  }
  .submit {
    display: inline-block;
    width: 80px;
    cursor: pointer;
  }
}
textarea.ant-input {
  min-height: 125px !important;
}
.disabled {
  background-color: #ccc !important;
  color: #fff !important;
}
.no_style {
  border-top: none;
  padding-top: 0;
}
</style>